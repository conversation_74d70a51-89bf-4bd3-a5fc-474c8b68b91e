# 用户登录状态刷新Bug修复测试指南

## 问题描述
用户登录系统后，刷新浏览器页面时右上角的用户信息会消失，变成未登录状态。

## 修复内容

### 1. Supabase客户端配置优化
- ✅ 启用会话持久化 (`persistSession: true`)
- ✅ 启用自动刷新token (`autoRefreshToken: true`)
- ✅ 启用URL会话检测 (`detectSessionInUrl: true`)
- ✅ 自定义存储键前缀 (`storageKey: 'narratoai-auth-token'`)
- ✅ 自定义localStorage存储实现

### 2. AuthContext会话管理优化
- ✅ 改进初始化会话恢复逻辑
- ✅ 增强错误处理和重试机制
- ✅ 添加详细的调试日志

### 3. 调试工具
- ✅ 创建会话调试工具 (`SessionDebugger`)
- ✅ 监控localStorage变化
- ✅ 提供全局调试函数

## 测试步骤

### 步骤1: 基础登录测试
1. 打开浏览器访问 http://localhost:5174/
2. 点击右上角"登录"按钮
3. 使用有效的邮箱和密码登录
4. 确认登录成功，右上角显示用户信息

### 步骤2: 刷新页面测试
1. 在已登录状态下，按 F5 或 Ctrl+R 刷新页面
2. 观察右上角用户信息是否保持显示
3. 检查浏览器控制台的调试日志

### 步骤3: 多次刷新测试
1. 连续刷新页面3-5次
2. 确认每次刷新后用户状态都能正确恢复
3. 观察控制台日志中的会话恢复过程

### 步骤4: 浏览器标签页测试
1. 在新标签页中打开同一网站
2. 确认新标签页中也能正确显示登录状态
3. 在一个标签页中退出登录，检查其他标签页是否同步更新

### 步骤5: 长时间会话测试
1. 保持登录状态超过1小时（token默认过期时间）
2. 刷新页面，确认自动刷新token功能正常工作
3. 检查控制台是否有token刷新的日志

## 调试工具使用

在浏览器控制台中可以使用以下调试命令：

```javascript
// 检查当前会话状态
window.debugSession.check()

// 强制刷新会话
window.debugSession.refresh()

// 获取详细会话信息
window.debugSession.info()

// 清理所有认证数据
window.debugSession.clear()
```

## 预期结果

### 修复前的问题
- ❌ 刷新页面后用户状态丢失
- ❌ 需要重新登录才能恢复状态
- ❌ localStorage中的token无法正确恢复

### 修复后的预期
- ✅ 刷新页面后用户状态保持
- ✅ 自动从localStorage恢复会话
- ✅ Token自动刷新，无需重新登录
- ✅ 详细的调试日志帮助问题排查

## 常见问题排查

### 如果修复后仍有问题
1. 检查浏览器控制台的错误日志
2. 使用 `window.debugSession.info()` 查看详细状态
3. 检查localStorage中是否有 `narratoai-auth-token` 相关键
4. 确认Supabase环境变量配置正确

### 清理测试环境
如果需要重置测试环境：
1. 在控制台运行 `window.debugSession.clear()`
2. 或手动清理浏览器localStorage
3. 刷新页面重新开始测试

## 技术细节

### 关键配置变更
- Supabase客户端现在使用持久化存储
- AuthContext使用异步初始化逻辑
- 增加了会话状态监控和调试功能

### 存储机制
- 会话数据存储在localStorage中
- 使用自定义键前缀避免冲突
- 支持跨标签页会话同步
