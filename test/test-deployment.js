/**
 * 简单的部署测试脚本
 * 测试已部署的Edge Functions是否正常工作
 */

const SUPABASE_URL = 'https://poijzwpvytxbhssbrrew.supabase.co'
const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InBvaWp6d3B2eXR4Ymhzc2JycmV3Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTA3NTE1ODksImV4cCI6MjA2NjMyNzU4OX0.PiPSm8IepAF1_zj4Oa0h8hWmoh1OAm-06Ths-6iOuhE'

async function testMovieSearch() {
  console.log('🔍 测试电影搜索功能...')
  
  try {
    const response = await fetch(`${SUPABASE_URL}/functions/v1/movie-search?query=test&page=1`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${SUPABASE_ANON_KEY}`,
        'Content-Type': 'application/json'
      }
    })
    
    console.log(`状态码: ${response.status}`)
    const data = await response.text()
    console.log('响应:', data.substring(0, 200) + '...')
    
    if (response.status === 200) {
      console.log('✅ movie-search 函数部署成功！')
    } else {
      console.log('⚠️ movie-search 函数可能需要配置 TMDB_API_KEY')
    }
  } catch (error) {
    console.error('❌ movie-search 测试失败:', error.message)
  }
}

async function testMovieDetails() {
  console.log('\n🎬 测试电影详情功能...')
  
  try {
    const response = await fetch(`${SUPABASE_URL}/functions/v1/movie-details?id=550&type=movie`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${SUPABASE_ANON_KEY}`,
        'Content-Type': 'application/json'
      }
    })
    
    console.log(`状态码: ${response.status}`)
    const data = await response.text()
    console.log('响应:', data.substring(0, 200) + '...')
    
    if (response.status === 200) {
      console.log('✅ movie-details 函数部署成功！')
    } else {
      console.log('⚠️ movie-details 函数可能需要配置 TMDB_API_KEY')
    }
  } catch (error) {
    console.error('❌ movie-details 测试失败:', error.message)
  }
}

async function main() {
  console.log('🚀 开始测试已部署的 Edge Functions...\n')
  
  await testMovieSearch()
  await testMovieDetails()
  
  console.log('\n📝 注意事项:')
  console.log('1. 如果看到 TMDB API 相关错误，请设置正确的 TMDB_API_KEY:')
  console.log('   supabase secrets set TMDB_API_KEY=your_real_api_key')
  console.log('2. 函数URL:')
  console.log(`   - 搜索: ${SUPABASE_URL}/functions/v1/movie-search`)
  console.log(`   - 详情: ${SUPABASE_URL}/functions/v1/movie-details`)
  console.log('3. 在 Supabase Dashboard 中查看函数日志和监控')
}

// 运行测试
main().catch(console.error)
