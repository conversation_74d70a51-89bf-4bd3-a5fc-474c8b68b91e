import { useState, useCallback, useMemo } from 'react';

export interface PaginationState {
  currentPage: number;
  totalPages: number;
  totalItems: number;
  itemsPerPage: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
}

export interface PaginationActions {
  goToPage: (page: number) => void;
  nextPage: () => void;
  previousPage: () => void;
  setItemsPerPage: (itemsPerPage: number) => void;
  setTotalItems: (totalItems: number) => void;
  reset: () => void;
}

export interface UsePaginationOptions {
  initialPage?: number;
  initialItemsPerPage?: number;
  totalItems?: number;
}

export interface UsePaginationReturn {
  state: PaginationState;
  actions: PaginationActions;
  getPageItems: <T>(items: T[]) => T[];
  getPageNumbers: () => number[];
}

/**
 * 分页管理Hook
 * 提供分页状态管理和相关操作
 */
export function usePagination({
  initialPage = 1,
  initialItemsPerPage = 20,
  totalItems = 0
}: UsePaginationOptions = {}): UsePaginationReturn {
  const [currentPage, setCurrentPage] = useState(initialPage);
  const [itemsPerPage, setItemsPerPage] = useState(initialItemsPerPage);
  const [totalItemsCount, setTotalItemsCount] = useState(totalItems);

  // 计算总页数
  const totalPages = useMemo(() => {
    return Math.ceil(totalItemsCount / itemsPerPage);
  }, [totalItemsCount, itemsPerPage]);

  // 分页状态
  const state: PaginationState = useMemo(() => ({
    currentPage,
    totalPages,
    totalItems: totalItemsCount,
    itemsPerPage,
    hasNextPage: currentPage < totalPages,
    hasPreviousPage: currentPage > 1
  }), [currentPage, totalPages, totalItemsCount, itemsPerPage]);

  // 跳转到指定页面
  const goToPage = useCallback((page: number) => {
    const validPage = Math.max(1, Math.min(page, totalPages));
    setCurrentPage(validPage);
  }, [totalPages]);

  // 下一页
  const nextPage = useCallback(() => {
    if (state.hasNextPage) {
      setCurrentPage(prev => prev + 1);
    }
  }, [state.hasNextPage]);

  // 上一页
  const previousPage = useCallback(() => {
    if (state.hasPreviousPage) {
      setCurrentPage(prev => prev - 1);
    }
  }, [state.hasPreviousPage]);

  // 设置每页项目数
  const setItemsPerPageAction = useCallback((newItemsPerPage: number) => {
    setItemsPerPage(newItemsPerPage);
    // 重新计算当前页，保持当前位置
    const currentIndex = (currentPage - 1) * itemsPerPage;
    const newPage = Math.floor(currentIndex / newItemsPerPage) + 1;
    setCurrentPage(newPage);
  }, [currentPage, itemsPerPage]);

  // 设置总项目数
  const setTotalItemsAction = useCallback((newTotalItems: number) => {
    setTotalItemsCount(newTotalItems);
    // 如果当前页超出范围，调整到最后一页
    const newTotalPages = Math.ceil(newTotalItems / itemsPerPage);
    if (currentPage > newTotalPages && newTotalPages > 0) {
      setCurrentPage(newTotalPages);
    }
  }, [currentPage, itemsPerPage]);

  // 重置分页状态
  const reset = useCallback(() => {
    setCurrentPage(initialPage);
    setItemsPerPage(initialItemsPerPage);
    setTotalItemsCount(totalItems);
  }, [initialPage, initialItemsPerPage, totalItems]);

  // 获取当前页的项目
  const getPageItems = useCallback(<T>(items: T[]): T[] => {
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    return items.slice(startIndex, endIndex);
  }, [currentPage, itemsPerPage]);

  // 获取页码数组（用于分页导航）
  const getPageNumbers = useCallback((): number[] => {
    const delta = 2; // 当前页前后显示的页数
    const range = [];
    const rangeWithDots = [];

    for (let i = Math.max(2, currentPage - delta); 
         i <= Math.min(totalPages - 1, currentPage + delta); 
         i++) {
      range.push(i);
    }

    if (currentPage - delta > 2) {
      rangeWithDots.push(1, -1); // -1 表示省略号
    } else {
      rangeWithDots.push(1);
    }

    rangeWithDots.push(...range);

    if (currentPage + delta < totalPages - 1) {
      rangeWithDots.push(-1, totalPages); // -1 表示省略号
    } else if (totalPages > 1) {
      rangeWithDots.push(totalPages);
    }

    return rangeWithDots;
  }, [currentPage, totalPages]);

  const actions: PaginationActions = {
    goToPage,
    nextPage,
    previousPage,
    setItemsPerPage: setItemsPerPageAction,
    setTotalItems: setTotalItemsAction,
    reset
  };

  return {
    state,
    actions,
    getPageItems,
    getPageNumbers
  };
}

/**
 * 无限滚动分页Hook
 * 用于实现无限滚动加载
 */
export interface UseInfiniteScrollOptions {
  initialPage?: number;
  hasMore?: boolean;
  isLoading?: boolean;
  threshold?: number; // 触发加载的距离阈值（像素）
}

export interface UseInfiniteScrollReturn {
  page: number;
  loadMore: () => void;
  reset: () => void;
  observerRef: (node: HTMLElement | null) => void;
}

export function useInfiniteScroll({
  initialPage = 1,
  hasMore = true,
  isLoading = false,
  threshold = 100
}: UseInfiniteScrollOptions = {}): UseInfiniteScrollReturn {
  const [page, setPage] = useState(initialPage);

  const loadMore = useCallback(() => {
    if (!isLoading && hasMore) {
      setPage(prev => prev + 1);
    }
  }, [isLoading, hasMore]);

  const reset = useCallback(() => {
    setPage(initialPage);
  }, [initialPage]);

  // Intersection Observer 回调
  const observerRef = useCallback((node: HTMLElement | null) => {
    if (isLoading || !hasMore) return;

    const observer = new IntersectionObserver(
      (entries) => {
        if (entries[0].isIntersecting) {
          loadMore();
        }
      },
      {
        rootMargin: `${threshold}px`
      }
    );

    if (node) observer.observe(node);

    return () => {
      if (node) observer.unobserve(node);
    };
  }, [isLoading, hasMore, threshold, loadMore]);

  return {
    page,
    loadMore,
    reset,
    observerRef
  };
}

/**
 * 虚拟滚动Hook
 * 用于大量数据的性能优化
 */
export interface UseVirtualScrollOptions {
  itemHeight: number;
  containerHeight: number;
  overscan?: number; // 额外渲染的项目数量
}

export interface UseVirtualScrollReturn {
  startIndex: number;
  endIndex: number;
  totalHeight: number;
  offsetY: number;
  scrollToIndex: (index: number) => void;
  onScroll: (scrollTop: number) => void;
}

export function useVirtualScroll({
  itemHeight,
  containerHeight,
  overscan = 5
}: UseVirtualScrollOptions) {
  const [scrollTop, setScrollTop] = useState(0);

  const startIndex = Math.max(0, Math.floor(scrollTop / itemHeight) - overscan);
  const endIndex = Math.min(
    startIndex + Math.ceil(containerHeight / itemHeight) + overscan * 2
  );

  const onScroll = useCallback((newScrollTop: number) => {
    setScrollTop(newScrollTop);
  }, []);

  const scrollToIndex = useCallback((index: number) => {
    const newScrollTop = index * itemHeight;
    setScrollTop(newScrollTop);
  }, [itemHeight]);

  return {
    startIndex,
    endIndex,
    totalHeight: 0, // 需要根据实际项目数量计算
    offsetY: startIndex * itemHeight,
    scrollToIndex,
    onScroll
  };
}
