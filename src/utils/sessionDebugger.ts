/**
 * 会话调试工具
 * 用于监控和调试Supabase认证会话状态
 */

import { supabase } from '../lib/supabase';

export class SessionDebugger {
  private static instance: SessionDebugger;
  private isEnabled: boolean = true;

  private constructor() {
    this.setupSessionMonitoring();
  }

  public static getInstance(): SessionDebugger {
    if (!SessionDebugger.instance) {
      SessionDebugger.instance = new SessionDebugger();
    }
    return SessionDebugger.instance;
  }

  /**
   * 启用或禁用调试模式
   */
  public setEnabled(enabled: boolean): void {
    this.isEnabled = enabled;
  }

  /**
   * 设置会话监控
   */
  private setupSessionMonitoring(): void {
    // 监控localStorage变化
    if (typeof window !== 'undefined') {
      const originalSetItem = localStorage.setItem;
      const originalRemoveItem = localStorage.removeItem;

      localStorage.setItem = (key: string, value: string) => {
        if (this.isEnabled && key.includes('narratoai-auth-token')) {
          console.log('🔐 localStorage设置认证token:', { key, valueLength: value.length });
        }
        return originalSetItem.call(localStorage, key, value);
      };

      localStorage.removeItem = (key: string) => {
        if (this.isEnabled && key.includes('narratoai-auth-token')) {
          console.log('🗑️ localStorage删除认证token:', { key });
        }
        return originalRemoveItem.call(localStorage, key);
      };
    }
  }

  /**
   * 检查当前会话状态
   */
  public async checkSessionStatus(): Promise<void> {
    if (!this.isEnabled) return;

    try {
      const { data: { session }, error } = await supabase.auth.getSession();

      console.log('🔍 会话状态检查:', {
        hasSession: !!session,
        hasUser: !!session?.user,
        userEmail: session?.user?.email,
        expiresAt: session?.expires_at ? new Date(session.expires_at * 1000).toISOString() : null,
        accessToken: session?.access_token ? `${session.access_token.substring(0, 20)}...` : null,
        refreshToken: session?.refresh_token ? `${session.refresh_token.substring(0, 20)}...` : null,
        error: error?.message
      });

      // 检查localStorage中的token
      if (typeof window !== 'undefined') {
        const storageKeys = Object.keys(localStorage).filter(key =>
          key.includes('narratoai-auth-token') || key.includes('supabase')
        );
        console.log('💾 localStorage中的认证相关键:', storageKeys);

        // 显示实际存储的内容
        storageKeys.forEach(key => {
          const value = localStorage.getItem(key);
          if (value) {
            try {
              const parsed = JSON.parse(value);
              console.log(`📄 ${key}:`, {
                hasAccessToken: !!parsed.access_token,
                hasRefreshToken: !!parsed.refresh_token,
                expiresAt: parsed.expires_at ? new Date(parsed.expires_at * 1000).toISOString() : null,
                user: parsed.user ? { id: parsed.user.id, email: parsed.user.email } : null
              });
            } catch (e) {
              console.log(`📄 ${key}: (非JSON格式)`, value.substring(0, 100));
            }
          }
        });
      }

    } catch (error) {
      console.error('❌ 检查会话状态时出错:', error);
    }
  }

  /**
   * 强制刷新会话
   */
  public async refreshSession(): Promise<void> {
    if (!this.isEnabled) return;

    try {
      console.log('🔄 强制刷新会话...');
      const { data, error } = await supabase.auth.refreshSession();

      console.log('🔄 会话刷新结果:', {
        success: !error,
        hasSession: !!data.session,
        hasUser: !!data.user,
        error: error?.message
      });

    } catch (error) {
      console.error('❌ 刷新会话时出错:', error);
    }
  }

  /**
   * 尝试从localStorage强制恢复会话
   */
  public async forceRecoverSession(): Promise<boolean> {
    if (!this.isEnabled) return false;

    try {
      console.log('🔧 尝试强制恢复会话...');

      if (typeof window === 'undefined') return false;

      // 查找所有可能的认证token
      const authKeys = Object.keys(localStorage).filter(key =>
        key.includes('narratoai-auth-token') || key.includes('supabase')
      );

      for (const key of authKeys) {
        const value = localStorage.getItem(key);
        if (!value) continue;

        try {
          const sessionData = JSON.parse(value);
          if (sessionData.access_token && sessionData.refresh_token) {
            console.log('🔑 找到会话数据，尝试恢复...', {
              key,
              hasAccessToken: !!sessionData.access_token,
              hasRefreshToken: !!sessionData.refresh_token,
              expiresAt: sessionData.expires_at ? new Date(sessionData.expires_at * 1000).toISOString() : null
            });

            // 方法1: 尝试使用 setSession（带超时）
            console.log('🔄 方法1: 正在调用 setSession...');

            try {
              // 创建一个带超时的Promise
              const setSessionPromise = supabase.auth.setSession({
                access_token: sessionData.access_token,
                refresh_token: sessionData.refresh_token
              });

              const timeoutPromise = new Promise((_, reject) => {
                setTimeout(() => reject(new Error('setSession timeout after 10 seconds')), 10000);
              });

              const { data, error } = await Promise.race([setSessionPromise, timeoutPromise]) as any;

              console.log('📋 setSession 结果:', {
                hasData: !!data,
                hasSession: !!data?.session,
                hasUser: !!data?.user,
                error: error?.message,
                errorCode: error?.status
              });

              if (!error && data.session) {
                console.log('✅ 方法1成功: 会话恢复成功!', {
                  userId: data.user?.id,
                  email: data.user?.email
                });
                return true;
              } else {
                console.log('❌ 方法1失败，尝试方法2...', {
                  error: error?.message,
                  hasData: !!data
                });
              }
            } catch (setSessionError) {
              console.log('❌ 方法1异常:', setSessionError.message);
            }

            // 方法2: 尝试刷新token
            try {
              console.log('🔄 方法2: 尝试刷新token...');

              const refreshPromise = supabase.auth.refreshSession({
                refresh_token: sessionData.refresh_token
              });

              const timeoutPromise = new Promise((_, reject) => {
                setTimeout(() => reject(new Error('refreshSession timeout after 10 seconds')), 10000);
              });

              const refreshResult = await Promise.race([refreshPromise, timeoutPromise]) as any;

              console.log('📋 refreshSession 结果:', {
                hasData: !!refreshResult.data,
                hasSession: !!refreshResult.data?.session,
                hasUser: !!refreshResult.data?.user,
                error: refreshResult.error?.message
              });

              if (!refreshResult.error && refreshResult.data.session) {
                console.log('✅ 方法2成功: token刷新成功!');
                return true;
              } else {
                console.log('❌ 方法2失败:', refreshResult.error?.message);
              }
            } catch (refreshError) {
              console.log('❌ 方法2异常:', refreshError.message);
            }

            // 方法3: 尝试直接验证token
            try {
              console.log('🔄 方法3: 尝试直接验证token...');

              // 直接使用getUser来验证当前token是否有效
              const { data: userData, error: userError } = await supabase.auth.getUser();

              console.log('📋 getUser 结果:', {
                hasUser: !!userData.user,
                userId: userData.user?.id,
                email: userData.user?.email,
                error: userError?.message
              });

              if (!userError && userData.user) {
                console.log('✅ 方法3成功: token验证成功，用户已存在!');
                return true;
              } else {
                console.log('❌ 方法3失败: token无效');
              }
            } catch (userError) {
              console.log('❌ 方法3异常:', userError.message);
            }
          }
        } catch (parseError) {
          console.log('⚠️ 解析会话数据失败:', key, parseError);
        }
      }

      console.log('❌ 所有恢复方法都失败了');
      return false;
    } catch (error) {
      console.error('❌ 强制恢复会话时出错:', error);
      return false;
    }
  }

  /**
   * 清理所有认证数据
   */
  public clearAuthData(): void {
    if (!this.isEnabled) return;

    console.log('🧹 清理所有认证数据...');
    
    if (typeof window !== 'undefined') {
      const authKeys = Object.keys(localStorage).filter(key => 
        key.includes('narratoai-auth-token') || key.includes('supabase')
      );
      
      authKeys.forEach(key => {
        localStorage.removeItem(key);
        console.log('🗑️ 已删除:', key);
      });
    }
  }

  /**
   * 获取详细的会话信息
   */
  public async getDetailedSessionInfo(): Promise<any> {
    try {
      const { data: { session }, error } = await supabase.auth.getSession();
      const { data: { user }, error: userError } = await supabase.auth.getUser();

      return {
        session: {
          exists: !!session,
          expiresAt: session?.expires_at,
          tokenType: session?.token_type,
          providerToken: session?.provider_token,
        },
        user: {
          exists: !!user,
          id: user?.id,
          email: user?.email,
          emailConfirmed: user?.email_confirmed_at,
          lastSignIn: user?.last_sign_in_at,
        },
        errors: {
          sessionError: error?.message,
          userError: userError?.message,
        },
        localStorage: typeof window !== 'undefined' ? 
          Object.keys(localStorage).filter(key => 
            key.includes('narratoai-auth-token') || key.includes('supabase')
          ) : []
      };
    } catch (error) {
      return { error: error.message };
    }
  }
}

// 创建全局实例
export const sessionDebugger = SessionDebugger.getInstance();

// 在开发环境下自动启用
if (import.meta.env.DEV) {
  sessionDebugger.setEnabled(true);

  // 确保调试工具在页面加载时正确初始化
  const initializeDebugTools = () => {
    if (typeof window !== 'undefined') {
      (window as any).debugSession = {
        check: async () => {
          console.log('🔍 开始检查会话状态...');
          await sessionDebugger.checkSessionStatus();
          return 'Check completed - see console logs above';
        },
        refresh: async () => {
          console.log('🔄 开始刷新会话...');
          await sessionDebugger.refreshSession();
          return 'Refresh completed - see console logs above';
        },
        clear: () => {
          console.log('🧹 开始清理认证数据...');
          sessionDebugger.clearAuthData();
          return 'Auth data cleared';
        },
        info: async () => {
          console.log('📊 获取详细会话信息...');
          const result = await sessionDebugger.getDetailedSessionInfo();
          console.log('📊 详细会话信息:', result);
          return result;
        },
        recover: async () => {
          console.log('🔧 开始强制恢复会话...');
          const result = await sessionDebugger.forceRecoverSession();
          console.log('🔧 恢复结果:', result ? 'Recovery successful' : 'Recovery failed');
          return result ? 'Recovery successful' : 'Recovery failed';
        },
        // 新增：简单的重新加载页面方法
        reload: () => {
          console.log('🔄 重新加载页面...');
          window.location.reload();
        },
        // 新增：检查localStorage数据格式
        checkStorage: () => {
          console.log('🔍 检查localStorage数据格式...');
          if (typeof window !== 'undefined') {
            const authKeys = Object.keys(localStorage).filter(key =>
              key.includes('narratoai-auth-token') || key.includes('supabase')
            );

            authKeys.forEach(key => {
              const value = localStorage.getItem(key);
              console.log(`📄 ${key}:`, value);
              if (value) {
                try {
                  const parsed = JSON.parse(value);
                  console.log(`📋 ${key} 解析后:`, parsed);
                } catch (e) {
                  console.log(`❌ ${key} 解析失败:`, e.message);
                }
              }
            });
          }
          return 'Storage check completed';
        },
        // 新增：强制修复方法
        forcefix: () => {
          console.log('🔧 尝试强制修复...');

          if (typeof window === 'undefined') return 'Not in browser';

          // 查找认证token
          const authKeys = Object.keys(localStorage).filter(key =>
            key.includes('narratoai-auth-token') || key.includes('supabase')
          );

          for (const key of authKeys) {
            const value = localStorage.getItem(key);
            if (!value) continue;

            try {
              const sessionData = JSON.parse(value);
              if (sessionData.access_token && sessionData.refresh_token) {
                console.log('🔑 找到会话数据，尝试重新设置...');

                // 删除现有的key
                localStorage.removeItem(key);

                // 使用标准的supabase key重新设置
                const standardKey = `sb-${window.location.hostname.replace(/\./g, '-')}-auth-token`;
                localStorage.setItem(standardKey, value);

                console.log('✅ 已重新设置localStorage key:', standardKey);
                console.log('🔄 即将重新加载页面...');

                setTimeout(() => {
                  window.location.reload();
                }, 1000);

                return 'Force fix applied, reloading...';
              }
            } catch (e) {
              console.log('❌ 解析失败:', e.message);
            }
          }

          return 'No valid session data found';
        },
      };

      console.log('🔧 会话调试工具已启用，使用 window.debugSession 进行调试');
      console.log('📋 可用命令: check(), refresh(), clear(), info(), recover()');
      console.log('💡 提示: 使用 await 或 .then() 来查看异步函数结果');
    }
  };

  // 立即初始化
  initializeDebugTools();

  // 确保在DOM加载完成后也初始化（防止页面刷新问题）
  if (typeof window !== 'undefined') {
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', initializeDebugTools);
    } else {
      // DOM已经加载完成，立即初始化
      setTimeout(initializeDebugTools, 100);
    }
  }
}
