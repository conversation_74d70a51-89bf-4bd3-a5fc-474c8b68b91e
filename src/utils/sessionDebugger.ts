/**
 * 会话调试工具
 * 用于监控和调试Supabase认证会话状态
 */

import { supabase } from '../lib/supabase';

export class SessionDebugger {
  private static instance: SessionDebugger;
  private isEnabled: boolean = true;

  private constructor() {
    this.setupSessionMonitoring();
  }

  public static getInstance(): SessionDebugger {
    if (!SessionDebugger.instance) {
      SessionDebugger.instance = new SessionDebugger();
    }
    return SessionDebugger.instance;
  }

  /**
   * 启用或禁用调试模式
   */
  public setEnabled(enabled: boolean): void {
    this.isEnabled = enabled;
  }

  /**
   * 设置会话监控
   */
  private setupSessionMonitoring(): void {
    // 监控localStorage变化
    if (typeof window !== 'undefined') {
      const originalSetItem = localStorage.setItem;
      const originalRemoveItem = localStorage.removeItem;

      localStorage.setItem = (key: string, value: string) => {
        if (this.isEnabled && key.includes('narratoai-auth-token')) {
          console.log('🔐 localStorage设置认证token:', { key, valueLength: value.length });
        }
        return originalSetItem.call(localStorage, key, value);
      };

      localStorage.removeItem = (key: string) => {
        if (this.isEnabled && key.includes('narratoai-auth-token')) {
          console.log('🗑️ localStorage删除认证token:', { key });
        }
        return originalRemoveItem.call(localStorage, key);
      };
    }
  }

  /**
   * 检查当前会话状态
   */
  public async checkSessionStatus(): Promise<void> {
    if (!this.isEnabled) return;

    try {
      const { data: { session }, error } = await supabase.auth.getSession();

      console.log('🔍 会话状态检查:', {
        hasSession: !!session,
        hasUser: !!session?.user,
        userEmail: session?.user?.email,
        expiresAt: session?.expires_at ? new Date(session.expires_at * 1000).toISOString() : null,
        accessToken: session?.access_token ? `${session.access_token.substring(0, 20)}...` : null,
        refreshToken: session?.refresh_token ? `${session.refresh_token.substring(0, 20)}...` : null,
        error: error?.message
      });

      // 检查localStorage中的token
      if (typeof window !== 'undefined') {
        const storageKeys = Object.keys(localStorage).filter(key =>
          key.includes('narratoai-auth-token') || key.includes('supabase')
        );
        console.log('💾 localStorage中的认证相关键:', storageKeys);

        // 显示实际存储的内容
        storageKeys.forEach(key => {
          const value = localStorage.getItem(key);
          if (value) {
            try {
              const parsed = JSON.parse(value);
              console.log(`📄 ${key}:`, {
                hasAccessToken: !!parsed.access_token,
                hasRefreshToken: !!parsed.refresh_token,
                expiresAt: parsed.expires_at ? new Date(parsed.expires_at * 1000).toISOString() : null,
                user: parsed.user ? { id: parsed.user.id, email: parsed.user.email } : null
              });
            } catch (e) {
              console.log(`📄 ${key}: (非JSON格式)`, value.substring(0, 100));
            }
          }
        });
      }

    } catch (error) {
      console.error('❌ 检查会话状态时出错:', error);
    }
  }

  /**
   * 强制刷新会话
   */
  public async refreshSession(): Promise<void> {
    if (!this.isEnabled) return;

    try {
      console.log('🔄 强制刷新会话...');
      const { data, error } = await supabase.auth.refreshSession();

      console.log('🔄 会话刷新结果:', {
        success: !error,
        hasSession: !!data.session,
        hasUser: !!data.user,
        error: error?.message
      });

    } catch (error) {
      console.error('❌ 刷新会话时出错:', error);
    }
  }

  /**
   * 尝试从localStorage强制恢复会话
   */
  public async forceRecoverSession(): Promise<boolean> {
    if (!this.isEnabled) return false;

    try {
      console.log('🔧 尝试强制恢复会话...');

      if (typeof window === 'undefined') return false;

      // 查找所有可能的认证token
      const authKeys = Object.keys(localStorage).filter(key =>
        key.includes('narratoai-auth-token') || key.includes('supabase')
      );

      for (const key of authKeys) {
        const value = localStorage.getItem(key);
        if (!value) continue;

        try {
          const sessionData = JSON.parse(value);
          if (sessionData.access_token && sessionData.refresh_token) {
            console.log('🔑 找到会话数据，尝试恢复...', {
              key,
              hasAccessToken: !!sessionData.access_token,
              hasRefreshToken: !!sessionData.refresh_token,
              expiresAt: sessionData.expires_at ? new Date(sessionData.expires_at * 1000).toISOString() : null
            });

            // 尝试使用找到的token设置会话
            const { data, error } = await supabase.auth.setSession({
              access_token: sessionData.access_token,
              refresh_token: sessionData.refresh_token
            });

            if (!error && data.session) {
              console.log('✅ 会话恢复成功!');
              return true;
            } else {
              console.log('❌ 会话恢复失败:', error?.message);
            }
          }
        } catch (parseError) {
          console.log('⚠️ 解析会话数据失败:', key, parseError);
        }
      }

      console.log('❌ 未找到有效的会话数据');
      return false;
    } catch (error) {
      console.error('❌ 强制恢复会话时出错:', error);
      return false;
    }
  }

  /**
   * 清理所有认证数据
   */
  public clearAuthData(): void {
    if (!this.isEnabled) return;

    console.log('🧹 清理所有认证数据...');
    
    if (typeof window !== 'undefined') {
      const authKeys = Object.keys(localStorage).filter(key => 
        key.includes('narratoai-auth-token') || key.includes('supabase')
      );
      
      authKeys.forEach(key => {
        localStorage.removeItem(key);
        console.log('🗑️ 已删除:', key);
      });
    }
  }

  /**
   * 获取详细的会话信息
   */
  public async getDetailedSessionInfo(): Promise<any> {
    try {
      const { data: { session }, error } = await supabase.auth.getSession();
      const { data: { user }, error: userError } = await supabase.auth.getUser();

      return {
        session: {
          exists: !!session,
          expiresAt: session?.expires_at,
          tokenType: session?.token_type,
          providerToken: session?.provider_token,
        },
        user: {
          exists: !!user,
          id: user?.id,
          email: user?.email,
          emailConfirmed: user?.email_confirmed_at,
          lastSignIn: user?.last_sign_in_at,
        },
        errors: {
          sessionError: error?.message,
          userError: userError?.message,
        },
        localStorage: typeof window !== 'undefined' ? 
          Object.keys(localStorage).filter(key => 
            key.includes('narratoai-auth-token') || key.includes('supabase')
          ) : []
      };
    } catch (error) {
      return { error: error.message };
    }
  }
}

// 创建全局实例
export const sessionDebugger = SessionDebugger.getInstance();

// 在开发环境下自动启用
if (import.meta.env.DEV) {
  sessionDebugger.setEnabled(true);
  
  // 添加全局调试函数
  if (typeof window !== 'undefined') {
    (window as any).debugSession = {
      check: () => sessionDebugger.checkSessionStatus(),
      refresh: () => sessionDebugger.refreshSession(),
      clear: () => sessionDebugger.clearAuthData(),
      info: () => sessionDebugger.getDetailedSessionInfo(),
      recover: () => sessionDebugger.forceRecoverSession(),
    };

    console.log('🔧 会话调试工具已启用，使用 window.debugSession 进行调试');
    console.log('📋 可用命令: check(), refresh(), clear(), info(), recover()');
  }
}
