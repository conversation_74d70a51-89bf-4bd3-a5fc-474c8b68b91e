/**
 * 会话调试工具
 * 用于监控和调试Supabase认证会话状态
 */

import { supabase } from '../lib/supabase';

export class SessionDebugger {
  private static instance: SessionDebugger;
  private isEnabled: boolean = true;

  private constructor() {
    this.setupSessionMonitoring();
  }

  public static getInstance(): SessionDebugger {
    if (!SessionDebugger.instance) {
      SessionDebugger.instance = new SessionDebugger();
    }
    return SessionDebugger.instance;
  }

  /**
   * 启用或禁用调试模式
   */
  public setEnabled(enabled: boolean): void {
    this.isEnabled = enabled;
  }

  /**
   * 设置会话监控
   */
  private setupSessionMonitoring(): void {
    // 监控localStorage变化
    if (typeof window !== 'undefined') {
      const originalSetItem = localStorage.setItem;
      const originalRemoveItem = localStorage.removeItem;

      localStorage.setItem = (key: string, value: string) => {
        if (this.isEnabled && key.includes('narratoai-auth-token')) {
          console.log('🔐 localStorage设置认证token:', { key, valueLength: value.length });
        }
        return originalSetItem.call(localStorage, key, value);
      };

      localStorage.removeItem = (key: string) => {
        if (this.isEnabled && key.includes('narratoai-auth-token')) {
          console.log('🗑️ localStorage删除认证token:', { key });
        }
        return originalRemoveItem.call(localStorage, key);
      };
    }
  }

  /**
   * 检查当前会话状态
   */
  public async checkSessionStatus(): Promise<void> {
    if (!this.isEnabled) return;

    try {
      const { data: { session }, error } = await supabase.auth.getSession();
      
      console.log('🔍 会话状态检查:', {
        hasSession: !!session,
        hasUser: !!session?.user,
        userEmail: session?.user?.email,
        expiresAt: session?.expires_at ? new Date(session.expires_at * 1000).toISOString() : null,
        accessToken: session?.access_token ? `${session.access_token.substring(0, 20)}...` : null,
        refreshToken: session?.refresh_token ? `${session.refresh_token.substring(0, 20)}...` : null,
        error: error?.message
      });

      // 检查localStorage中的token
      if (typeof window !== 'undefined') {
        const storageKeys = Object.keys(localStorage).filter(key => 
          key.includes('narratoai-auth-token') || key.includes('supabase')
        );
        console.log('💾 localStorage中的认证相关键:', storageKeys);
      }

    } catch (error) {
      console.error('❌ 检查会话状态时出错:', error);
    }
  }

  /**
   * 强制刷新会话
   */
  public async refreshSession(): Promise<void> {
    if (!this.isEnabled) return;

    try {
      console.log('🔄 强制刷新会话...');
      const { data, error } = await supabase.auth.refreshSession();
      
      console.log('🔄 会话刷新结果:', {
        success: !error,
        hasSession: !!data.session,
        hasUser: !!data.user,
        error: error?.message
      });

    } catch (error) {
      console.error('❌ 刷新会话时出错:', error);
    }
  }

  /**
   * 清理所有认证数据
   */
  public clearAuthData(): void {
    if (!this.isEnabled) return;

    console.log('🧹 清理所有认证数据...');
    
    if (typeof window !== 'undefined') {
      const authKeys = Object.keys(localStorage).filter(key => 
        key.includes('narratoai-auth-token') || key.includes('supabase')
      );
      
      authKeys.forEach(key => {
        localStorage.removeItem(key);
        console.log('🗑️ 已删除:', key);
      });
    }
  }

  /**
   * 获取详细的会话信息
   */
  public async getDetailedSessionInfo(): Promise<any> {
    try {
      const { data: { session }, error } = await supabase.auth.getSession();
      const { data: { user }, error: userError } = await supabase.auth.getUser();

      return {
        session: {
          exists: !!session,
          expiresAt: session?.expires_at,
          tokenType: session?.token_type,
          providerToken: session?.provider_token,
        },
        user: {
          exists: !!user,
          id: user?.id,
          email: user?.email,
          emailConfirmed: user?.email_confirmed_at,
          lastSignIn: user?.last_sign_in_at,
        },
        errors: {
          sessionError: error?.message,
          userError: userError?.message,
        },
        localStorage: typeof window !== 'undefined' ? 
          Object.keys(localStorage).filter(key => 
            key.includes('narratoai-auth-token') || key.includes('supabase')
          ) : []
      };
    } catch (error) {
      return { error: error.message };
    }
  }
}

// 创建全局实例
export const sessionDebugger = SessionDebugger.getInstance();

// 在开发环境下自动启用
if (import.meta.env.DEV) {
  sessionDebugger.setEnabled(true);
  
  // 添加全局调试函数
  if (typeof window !== 'undefined') {
    (window as any).debugSession = {
      check: () => sessionDebugger.checkSessionStatus(),
      refresh: () => sessionDebugger.refreshSession(),
      clear: () => sessionDebugger.clearAuthData(),
      info: () => sessionDebugger.getDetailedSessionInfo(),
    };
    
    console.log('🔧 会话调试工具已启用，使用 window.debugSession 进行调试');
  }
}
