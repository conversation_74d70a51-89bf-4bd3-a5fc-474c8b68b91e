/**
 * 搜索功能测试工具
 * 用于调试和验证搜索API的连通性
 */

import { supabase } from '../lib/supabase';

export interface TestResult {
  success: boolean;
  method: string;
  duration: number;
  error?: string;
  resultCount?: number;
  cached?: boolean;
}

/**
 * 测试Supabase Edge Function调用
 */
export async function testSupabaseSearch(query: string = 'test'): Promise<TestResult> {
  const startTime = Date.now();
  
  try {
    console.log('测试Supabase Edge Function调用...');
    
    const { data, error } = await supabase.functions.invoke('movie-search', {
      body: {
        query,
        page: 1,
        type: 'multi',
        language: 'zh-CN',
        include_adult: false
      }
    });

    const duration = Date.now() - startTime;

    if (error) {
      console.error('Supabase调用错误:', error);
      return {
        success: false,
        method: 'supabase',
        duration,
        error: error.message || '未知错误'
      };
    }

    if (!data || !data.success) {
      return {
        success: false,
        method: 'supabase',
        duration,
        error: data?.error || '服务器返回失败状态'
      };
    }

    console.log('Supabase调用成功:', data);
    
    return {
      success: true,
      method: 'supabase',
      duration,
      resultCount: data.data?.results?.length || 0,
      cached: data.cached || false
    };
  } catch (error) {
    const duration = Date.now() - startTime;
    console.error('Supabase调用异常:', error);
    
    return {
      success: false,
      method: 'supabase',
      duration,
      error: error instanceof Error ? error.message : '未知异常'
    };
  }
}

/**
 * 测试直接HTTP调用
 */
export async function testDirectHttpSearch(query: string = 'test'): Promise<TestResult> {
  const startTime = Date.now();
  
  try {
    console.log('测试直接HTTP调用...');
    
    const searchParams = new URLSearchParams({
      query,
      page: '1',
      type: 'multi',
      language: 'zh-CN',
      include_adult: 'false'
    });

    const response = await fetch(
      `${supabase.supabaseUrl}/functions/v1/movie-search?${searchParams}`,
      {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${supabase.supabaseKey}`,
          'Content-Type': 'application/json',
          'User-Agent': 'NarratoAI-Test/1.0'
        },
        signal: AbortSignal.timeout(10000) // 10秒超时
      }
    );

    const duration = Date.now() - startTime;

    if (!response.ok) {
      let errorMessage = `HTTP ${response.status}`;
      try {
        const errorData = await response.json();
        errorMessage = errorData.error || errorMessage;
      } catch {
        // 忽略JSON解析错误
      }
      
      return {
        success: false,
        method: 'http',
        duration,
        error: errorMessage
      };
    }

    const data = await response.json();
    
    if (!data.success) {
      return {
        success: false,
        method: 'http',
        duration,
        error: data.error || '服务器返回失败状态'
      };
    }

    console.log('HTTP调用成功:', data);
    
    return {
      success: true,
      method: 'http',
      duration,
      resultCount: data.data?.results?.length || 0,
      cached: data.cached || false
    };
  } catch (error) {
    const duration = Date.now() - startTime;
    console.error('HTTP调用异常:', error);
    
    return {
      success: false,
      method: 'http',
      duration,
      error: error instanceof Error ? error.message : '未知异常'
    };
  }
}

/**
 * 测试网络连通性
 */
export async function testNetworkConnectivity(): Promise<TestResult> {
  const startTime = Date.now();
  
  try {
    console.log('测试网络连通性...');
    
    // 测试基本的网络连接
    const response = await fetch('https://httpbin.org/get', {
      method: 'GET',
      signal: AbortSignal.timeout(5000)
    });

    const duration = Date.now() - startTime;

    if (!response.ok) {
      return {
        success: false,
        method: 'network',
        duration,
        error: `网络测试失败: HTTP ${response.status}`
      };
    }

    console.log('网络连通性正常');
    
    return {
      success: true,
      method: 'network',
      duration
    };
  } catch (error) {
    const duration = Date.now() - startTime;
    console.error('网络连通性测试失败:', error);
    
    return {
      success: false,
      method: 'network',
      duration,
      error: error instanceof Error ? error.message : '网络连接失败'
    };
  }
}

/**
 * 运行完整的搜索测试套件
 */
export async function runSearchTestSuite(query: string = '流浪地球'): Promise<{
  networkTest: TestResult;
  supabaseTest: TestResult;
  httpTest: TestResult;
  summary: {
    totalTests: number;
    passedTests: number;
    failedTests: number;
    averageDuration: number;
  };
}> {
  console.log(`开始运行搜索测试套件，查询词: "${query}"`);
  
  const networkTest = await testNetworkConnectivity();
  const supabaseTest = await testSupabaseSearch(query);
  const httpTest = await testDirectHttpSearch(query);
  
  const tests = [networkTest, supabaseTest, httpTest];
  const passedTests = tests.filter(test => test.success).length;
  const failedTests = tests.length - passedTests;
  const averageDuration = tests.reduce((sum, test) => sum + test.duration, 0) / tests.length;
  
  const summary = {
    totalTests: tests.length,
    passedTests,
    failedTests,
    averageDuration: Math.round(averageDuration)
  };
  
  console.log('测试套件完成:', summary);
  
  return {
    networkTest,
    supabaseTest,
    httpTest,
    summary
  };
}

/**
 * 在浏览器控制台中运行测试
 */
export function runTestInConsole(query?: string) {
  if (typeof window === 'undefined') {
    console.error('此函数只能在浏览器环境中运行');
    return;
  }
  
  console.log('开始搜索功能测试...');
  
  runSearchTestSuite(query).then(results => {
    console.group('🔍 搜索功能测试结果');
    
    console.group('📊 测试摘要');
    console.log(`总测试数: ${results.summary.totalTests}`);
    console.log(`通过测试: ${results.summary.passedTests}`);
    console.log(`失败测试: ${results.summary.failedTests}`);
    console.log(`平均耗时: ${results.summary.averageDuration}ms`);
    console.groupEnd();
    
    console.group('🌐 网络连通性测试');
    console.log(`状态: ${results.networkTest.success ? '✅ 通过' : '❌ 失败'}`);
    console.log(`耗时: ${results.networkTest.duration}ms`);
    if (results.networkTest.error) {
      console.error(`错误: ${results.networkTest.error}`);
    }
    console.groupEnd();
    
    console.group('🔧 Supabase调用测试');
    console.log(`状态: ${results.supabaseTest.success ? '✅ 通过' : '❌ 失败'}`);
    console.log(`耗时: ${results.supabaseTest.duration}ms`);
    if (results.supabaseTest.resultCount !== undefined) {
      console.log(`结果数量: ${results.supabaseTest.resultCount}`);
    }
    if (results.supabaseTest.cached) {
      console.log('🗄️ 使用了缓存');
    }
    if (results.supabaseTest.error) {
      console.error(`错误: ${results.supabaseTest.error}`);
    }
    console.groupEnd();
    
    console.group('🌍 HTTP直接调用测试');
    console.log(`状态: ${results.httpTest.success ? '✅ 通过' : '❌ 失败'}`);
    console.log(`耗时: ${results.httpTest.duration}ms`);
    if (results.httpTest.resultCount !== undefined) {
      console.log(`结果数量: ${results.httpTest.resultCount}`);
    }
    if (results.httpTest.cached) {
      console.log('🗄️ 使用了缓存');
    }
    if (results.httpTest.error) {
      console.error(`错误: ${results.httpTest.error}`);
    }
    console.groupEnd();
    
    console.groupEnd();
    
    // 提供建议
    if (results.summary.failedTests > 0) {
      console.group('💡 故障排除建议');
      
      if (!results.networkTest.success) {
        console.log('• 检查网络连接');
        console.log('• 确认防火墙设置');
      }
      
      if (!results.supabaseTest.success && !results.httpTest.success) {
        console.log('• 检查Supabase项目配置');
        console.log('• 验证API密钥是否正确');
        console.log('• 确认Edge Function是否已部署');
      }
      
      if (!results.supabaseTest.success && results.httpTest.success) {
        console.log('• Supabase客户端可能有问题，但HTTP调用正常');
        console.log('• 检查Supabase客户端配置');
      }
      
      if (results.supabaseTest.success && !results.httpTest.success) {
        console.log('• HTTP调用有问题，但Supabase客户端正常');
        console.log('• 检查CORS设置');
      }
      
      console.groupEnd();
    }
  }).catch(error => {
    console.error('测试套件运行失败:', error);
  });
}

// 在开发环境下将测试函数暴露到全局
if (typeof window !== 'undefined' && process.env.NODE_ENV === 'development') {
  (window as any).runSearchTest = runTestInConsole;
}
