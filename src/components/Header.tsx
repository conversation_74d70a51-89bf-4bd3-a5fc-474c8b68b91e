import React, { useState } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { Video, Sparkles } from 'lucide-react';
import { useAuth } from '../contexts/AuthContext';
import UserMenu from './UserMenu';
import AuthModal from './AuthModal';
import SubscriptionModal from './SubscriptionModal';

const Header: React.FC = () => {
  const location = useLocation();
  const isHome = location.pathname === '/';
  const { user, loading } = useAuth();

  // 调试信息
  console.log('🔍 Header组件状态:', { user: !!user, loading, userEmail: user?.email });

  const [showAuthModal, setShowAuthModal] = useState(false);
  const [showSubscriptionModal, setShowSubscriptionModal] = useState(false);
  const [authMode, setAuthMode] = useState<'signin' | 'signup'>('signin');

  const handleAuthClick = (mode: 'signin' | 'signup') => {
    setAuthMode(mode);
    setShowAuthModal(true);
  };

  return (
    <>
      <header className="fixed top-0 left-0 right-0 z-50 bg-white/80 backdrop-blur-md border-b border-slate-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <Link 
              to="/" 
              className="flex items-center space-x-3 hover:opacity-80 transition-opacity"
            >
              <div className="flex items-center justify-center w-10 h-10 bg-gradient-to-br from-primary-500 to-primary-600 rounded-xl shadow-lg">
                <Video className="w-5 h-5 text-white" />
              </div>
              <div>
                <h1 className="text-xl font-bold bg-gradient-to-r from-primary-600 to-secondary-600 bg-clip-text text-transparent">
                  NarratoAI
                </h1>
                <p className="text-xs text-slate-500 -mt-1">AI影视解说助手</p>
              </div>
            </Link>

            <nav className="flex items-center space-x-4 md:space-x-8">
              <Link
                to="/"
                className={`px-3 py-2 md:px-4 rounded-lg transition-all text-sm md:text-base ${
                  isHome
                    ? 'bg-primary-50 text-primary-600 font-medium'
                    : 'text-slate-600 hover:text-primary-600 hover:bg-slate-50'
                }`}
              >
                创作中心
              </Link>
              
              {!loading && (
                user ? (
                  <div className="flex items-center space-x-4">
                    <button 
                      onClick={() => setShowSubscriptionModal(true)}
                      className="flex items-center space-x-2 px-4 py-2 bg-gradient-to-r from-primary-500 to-primary-600 text-white rounded-lg hover:from-primary-600 hover:to-primary-700 transition-all shadow-lg hover:shadow-xl"
                    >
                      <Sparkles className="w-4 h-4" />
                      <span className="font-medium">升级Pro</span>
                    </button>
                    <UserMenu onOpenSubscription={() => setShowSubscriptionModal(true)} />
                  </div>
                ) : (
                  <div className="flex items-center space-x-2 md:space-x-3">
                    <button
                      onClick={() => handleAuthClick('signin')}
                      className="px-3 py-2 md:px-4 text-slate-600 hover:text-primary-600 hover:bg-slate-50 rounded-lg transition-all font-medium text-sm md:text-base"
                    >
                      登录
                    </button>
                    <button
                      onClick={() => handleAuthClick('signup')}
                      className="flex items-center space-x-1 md:space-x-2 px-3 py-2 md:px-4 bg-gradient-to-r from-primary-500 to-primary-600 text-white rounded-lg hover:from-primary-600 hover:to-primary-700 transition-all shadow-lg hover:shadow-xl font-medium text-sm md:text-base"
                    >
                      <Sparkles className="w-3 h-3 md:w-4 md:h-4" />
                      <span>免费开始</span>
                    </button>
                  </div>
                )
              )}
            </nav>
          </div>
        </div>
      </header>

      <AuthModal 
        isOpen={showAuthModal}
        onClose={() => setShowAuthModal(false)}
        initialMode={authMode}
      />

      <SubscriptionModal
        isOpen={showSubscriptionModal}
        onClose={() => setShowSubscriptionModal(false)}
      />
    </>
  );
};

export default Header;