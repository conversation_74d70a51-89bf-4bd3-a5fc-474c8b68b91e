import React, { useState } from 'react';

const SimpleSearchTest: React.FC = () => {
  const [query, setQuery] = useState('');
  const [isSearching, setIsSearching] = useState(false);
  const [results, setResults] = useState<any[]>([]);
  const [error, setError] = useState<string | null>(null);

  const handleSearch = async () => {
    if (!query.trim()) {
      setError('请输入搜索关键词');
      return;
    }

    console.log('🔍 SimpleSearchTest: 开始搜索', query);
    console.log('🔍 环境变量:', {
      url: import.meta.env.VITE_SUPABASE_URL,
      key: import.meta.env.VITE_SUPABASE_ANON_KEY ? '已设置' : '未设置'
    });

    setIsSearching(true);
    setError(null);
    setResults([]);

    const startTime = Date.now();

    try {
      const requestBody = {
        query: query.trim(),
        page: 1,
        type: 'multi',
        language: 'zh-CN',
        include_adult: false
      };

      console.log('📤 发送请求:', requestBody);

      const response = await fetch(
        `${import.meta.env.VITE_SUPABASE_URL}/functions/v1/movie-search`,
        {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${import.meta.env.VITE_SUPABASE_ANON_KEY}`,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(requestBody)
        }
      );

      const duration = Date.now() - startTime;
      console.log(`📡 SimpleSearchTest: 收到响应 (${duration}ms)`, response.status);

      if (!response.ok) {
        const errorText = await response.text();
        console.error('❌ 响应错误:', errorText);
        throw new Error(`HTTP ${response.status}: ${response.statusText}\n${errorText}`);
      }

      const data = await response.json();
      console.log('📊 SimpleSearchTest: 解析数据', data);

      if (!data.success) {
        throw new Error(data.error || '搜索失败');
      }

      setResults(data.data.results || []);
      console.log('✅ SimpleSearchTest: 搜索成功', data.data.results?.length);

    } catch (err) {
      const duration = Date.now() - startTime;
      console.error(`❌ SimpleSearchTest: 搜索失败 (${duration}ms)`, err);
      setError(err instanceof Error ? err.message : '搜索失败');
    } finally {
      console.log('🔄 SimpleSearchTest: 重置搜索状态');
      setIsSearching(false);
    }
  };

  return (
    <div className="p-6 max-w-2xl mx-auto">
      <h2 className="text-2xl font-bold mb-4">简单搜索测试</h2>
      
      <div className="mb-4">
        <input
          type="text"
          value={query}
          onChange={(e) => setQuery(e.target.value)}
          placeholder="输入搜索关键词..."
          className="w-full p-3 border border-gray-300 rounded-lg"
          disabled={isSearching}
        />
      </div>

      <button
        onClick={handleSearch}
        disabled={isSearching || !query.trim()}
        className="w-full p-3 bg-blue-600 text-white rounded-lg disabled:opacity-50 disabled:cursor-not-allowed"
      >
        {isSearching ? '搜索中...' : '搜索'}
      </button>

      {error && (
        <div className="mt-4 p-3 bg-red-100 border border-red-300 text-red-700 rounded-lg">
          错误: {error}
        </div>
      )}

      {results.length > 0 && (
        <div className="mt-4">
          <h3 className="text-lg font-semibold mb-2">搜索结果 ({results.length})</h3>
          <div className="space-y-2">
            {results.slice(0, 5).map((result, index) => (
              <div key={index} className="p-3 bg-gray-100 rounded-lg">
                <div className="font-medium">{result.title}</div>
                <div className="text-sm text-gray-600">
                  {result.media_type} • {result.release_date} • ⭐ {result.vote_average}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      <div className="mt-4 p-3 bg-gray-100 rounded-lg">
        <h4 className="font-semibold">调试信息</h4>
        <div className="text-sm text-gray-600">
          <div>搜索状态: {isSearching ? '搜索中' : '空闲'}</div>
          <div>查询词: {query}</div>
          <div>结果数量: {results.length}</div>
          <div>错误: {error || '无'}</div>
        </div>
      </div>
    </div>
  );
};

export default SimpleSearchTest;
