import React from 'react';
import { ChevronLeft, ChevronRight, MoreHorizontal } from 'lucide-react';
import { type PaginationState, type PaginationActions } from '../hooks/usePagination';

interface PaginationProps {
  state: PaginationState;
  actions: PaginationActions;
  showItemsPerPage?: boolean;
  showPageInfo?: boolean;
  className?: string;
}

const Pagination: React.FC<PaginationProps> = ({
  state,
  actions,
  showItemsPerPage = true,
  showPageInfo = true,
  className = ''
}) => {
  const { currentPage, totalPages, totalItems, itemsPerPage, hasNextPage, hasPreviousPage } = state;
  const { goToPage, nextPage, previousPage, setItemsPerPage } = actions;

  // 获取页码数组
  const getPageNumbers = (): (number | string)[] => {
    const delta = 2;
    const range = [];
    const rangeWithDots = [];

    // 计算显示范围
    for (let i = Math.max(2, currentPage - delta); 
         i <= Math.min(totalPages - 1, currentPage + delta); 
         i++) {
      range.push(i);
    }

    // 添加第一页
    if (currentPage - delta > 2) {
      rangeWithDots.push(1, '...');
    } else {
      rangeWithDots.push(1);
    }

    // 添加中间页码
    rangeWithDots.push(...range);

    // 添加最后一页
    if (currentPage + delta < totalPages - 1) {
      rangeWithDots.push('...', totalPages);
    } else if (totalPages > 1) {
      rangeWithDots.push(totalPages);
    }

    return rangeWithDots;
  };

  const pageNumbers = getPageNumbers();

  // 计算当前显示的项目范围
  const startItem = (currentPage - 1) * itemsPerPage + 1;
  const endItem = Math.min(currentPage * itemsPerPage, totalItems);

  if (totalPages <= 1) {
    return null; // 只有一页或没有数据时不显示分页
  }

  return (
    <div className={`flex flex-col sm:flex-row items-center justify-between space-y-4 sm:space-y-0 ${className}`}>
      {/* 页面信息 */}
      {showPageInfo && (
        <div className="flex items-center space-x-4 text-sm text-slate-600">
          <span>
            显示 {startItem} - {endItem} 项，共 {totalItems} 项
          </span>
          
          {/* 每页项目数选择 */}
          {showItemsPerPage && (
            <div className="flex items-center space-x-2">
              <span>每页显示:</span>
              <select
                value={itemsPerPage}
                onChange={(e) => setItemsPerPage(Number(e.target.value))}
                className="border border-slate-200 rounded px-2 py-1 text-sm focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              >
                <option value={10}>10</option>
                <option value={20}>20</option>
                <option value={50}>50</option>
                <option value={100}>100</option>
              </select>
            </div>
          )}
        </div>
      )}

      {/* 分页导航 */}
      <div className="flex items-center space-x-1">
        {/* 上一页按钮 */}
        <button
          onClick={previousPage}
          disabled={!hasPreviousPage}
          className="flex items-center justify-center w-8 h-8 rounded-lg border border-slate-200 text-slate-600 hover:bg-slate-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          aria-label="上一页"
        >
          <ChevronLeft className="w-4 h-4" />
        </button>

        {/* 页码按钮 */}
        {pageNumbers.map((pageNumber, index) => {
          if (pageNumber === '...') {
            return (
              <span
                key={`ellipsis-${index}`}
                className="flex items-center justify-center w-8 h-8 text-slate-400"
              >
                <MoreHorizontal className="w-4 h-4" />
              </span>
            );
          }

          const page = pageNumber as number;
          const isCurrentPage = page === currentPage;

          return (
            <button
              key={page}
              onClick={() => goToPage(page)}
              className={`flex items-center justify-center w-8 h-8 rounded-lg border text-sm font-medium transition-colors ${
                isCurrentPage
                  ? 'bg-primary-500 border-primary-500 text-white'
                  : 'border-slate-200 text-slate-600 hover:bg-slate-50'
              }`}
              aria-label={`第 ${page} 页`}
              aria-current={isCurrentPage ? 'page' : undefined}
            >
              {page}
            </button>
          );
        })}

        {/* 下一页按钮 */}
        <button
          onClick={nextPage}
          disabled={!hasNextPage}
          className="flex items-center justify-center w-8 h-8 rounded-lg border border-slate-200 text-slate-600 hover:bg-slate-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          aria-label="下一页"
        >
          <ChevronRight className="w-4 h-4" />
        </button>
      </div>
    </div>
  );
};

export default Pagination;

/**
 * 简化版分页组件
 * 只显示上一页/下一页按钮
 */
interface SimplePaginationProps {
  state: PaginationState;
  actions: PaginationActions;
  className?: string;
}

export const SimplePagination: React.FC<SimplePaginationProps> = ({
  state,
  actions,
  className = ''
}) => {
  const { currentPage, totalPages, hasNextPage, hasPreviousPage } = state;
  const { nextPage, previousPage } = actions;

  if (totalPages <= 1) {
    return null;
  }

  return (
    <div className={`flex items-center justify-between ${className}`}>
      <button
        onClick={previousPage}
        disabled={!hasPreviousPage}
        className="flex items-center space-x-2 px-4 py-2 text-sm font-medium text-slate-600 bg-white border border-slate-200 rounded-lg hover:bg-slate-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
      >
        <ChevronLeft className="w-4 h-4" />
        <span>上一页</span>
      </button>

      <span className="text-sm text-slate-600">
        第 {currentPage} 页，共 {totalPages} 页
      </span>

      <button
        onClick={nextPage}
        disabled={!hasNextPage}
        className="flex items-center space-x-2 px-4 py-2 text-sm font-medium text-slate-600 bg-white border border-slate-200 rounded-lg hover:bg-slate-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
      >
        <span>下一页</span>
        <ChevronRight className="w-4 h-4" />
      </button>
    </div>
  );
};

/**
 * 加载更多按钮组件
 * 用于无限滚动场景
 */
interface LoadMoreButtonProps {
  onLoadMore: () => void;
  isLoading: boolean;
  hasMore: boolean;
  className?: string;
}

export const LoadMoreButton: React.FC<LoadMoreButtonProps> = ({
  onLoadMore,
  isLoading,
  hasMore,
  className = ''
}) => {
  if (!hasMore) {
    return (
      <div className={`text-center py-8 ${className}`}>
        <p className="text-slate-500 text-sm">已显示全部结果</p>
      </div>
    );
  }

  return (
    <div className={`text-center py-8 ${className}`}>
      <button
        onClick={onLoadMore}
        disabled={isLoading}
        className="px-6 py-3 bg-primary-500 text-white rounded-lg hover:bg-primary-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
      >
        {isLoading ? (
          <div className="flex items-center space-x-2">
            <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
            <span>加载中...</span>
          </div>
        ) : (
          '加载更多'
        )}
      </button>
    </div>
  );
};

/**
 * 无限滚动触发器组件
 * 用于自动触发加载更多
 */
interface InfiniteScrollTriggerProps {
  onLoadMore: () => void;
  isLoading: boolean;
  hasMore: boolean;
  threshold?: number;
  className?: string;
}

export const InfiniteScrollTrigger: React.FC<InfiniteScrollTriggerProps> = ({
  onLoadMore,
  isLoading,
  hasMore,
  threshold = 100,
  className = ''
}) => {
  const observerRef = React.useCallback((node: HTMLDivElement | null) => {
    if (isLoading || !hasMore) return;

    const observer = new IntersectionObserver(
      (entries) => {
        if (entries[0].isIntersecting) {
          onLoadMore();
        }
      },
      {
        rootMargin: `${threshold}px`
      }
    );

    if (node) observer.observe(node);

    return () => {
      if (node) observer.unobserve(node);
    };
  }, [isLoading, hasMore, threshold, onLoadMore]);

  if (!hasMore) {
    return (
      <div className={`text-center py-8 ${className}`}>
        <p className="text-slate-500 text-sm">已显示全部结果</p>
      </div>
    );
  }

  return (
    <div ref={observerRef} className={`text-center py-8 ${className}`}>
      {isLoading && (
        <div className="flex items-center justify-center space-x-2 text-slate-500">
          <div className="w-5 h-5 border-2 border-slate-300 border-t-primary-500 rounded-full animate-spin"></div>
          <span>加载更多内容...</span>
        </div>
      )}
    </div>
  );
};
