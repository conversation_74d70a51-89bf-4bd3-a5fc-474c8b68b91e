import React, { useState, useEffect } from 'react';
import { 
  Clock, 
  Search, 
  X, 
  Trash2, 
  Download, 
  Upload, 
  BarChart3,
  Filter,
  Calendar
} from 'lucide-react';
import { 
  searchHistoryManager, 
  type SearchHistoryItem, 
  type SearchHistoryStats 
} from '../lib/searchHistory';

interface SearchHistoryProps {
  onSearchSelect: (query: string) => void;
  onClose?: () => void;
  maxItems?: number;
}

const SearchHistory: React.FC<SearchHistoryProps> = ({ 
  onSearchSelect, 
  onClose, 
  maxItems = 20 
}) => {
  const [history, setHistory] = useState<SearchHistoryItem[]>([]);
  const [stats, setStats] = useState<SearchHistoryStats | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [showStats, setShowStats] = useState(false);
  const [filterType, setFilterType] = useState<'all' | 'movie' | 'tv'>('all');

  // 加载搜索历史
  const loadHistory = () => {
    let historyItems = searchHistoryManager.getHistory();
    
    // 应用过滤器
    if (filterType !== 'all') {
      historyItems = historyItems.filter(item => item.mediaType === filterType);
    }
    
    // 应用搜索过滤
    if (searchTerm.trim()) {
      historyItems = searchHistoryManager.searchHistory(searchTerm, maxItems);
    } else {
      historyItems = historyItems.slice(0, maxItems);
    }
    
    setHistory(historyItems);
  };

  const loadStats = () => {
    setStats(searchHistoryManager.getStats());
  };

  useEffect(() => {
    loadHistory();
    loadStats();
  }, [searchTerm, filterType, maxItems]);

  const handleSearchSelect = (query: string) => {
    onSearchSelect(query);
    if (onClose) onClose();
  };

  const handleRemoveItem = (id: string, event: React.MouseEvent) => {
    event.stopPropagation();
    searchHistoryManager.removeSearch(id);
    loadHistory();
    loadStats();
  };

  const handleClearAll = () => {
    if (window.confirm('确定要清空所有搜索历史吗？此操作不可撤销。')) {
      searchHistoryManager.clearHistory();
      loadHistory();
      loadStats();
    }
  };

  const handleExport = () => {
    const data = searchHistoryManager.exportHistory();
    const blob = new Blob([data], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `search-history-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const handleImport = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        const content = e.target?.result as string;
        if (searchHistoryManager.importHistory(content)) {
          loadHistory();
          loadStats();
          alert('搜索历史导入成功！');
        } else {
          alert('导入失败，请检查文件格式。');
        }
      };
      reader.readAsText(file);
    }
    // 重置input值，允许重复选择同一文件
    event.target.value = '';
  };

  const formatDate = (timestamp: number) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
    
    if (diffDays === 0) {
      return date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });
    } else if (diffDays === 1) {
      return '昨天';
    } else if (diffDays < 7) {
      return `${diffDays}天前`;
    } else {
      return date.toLocaleDateString('zh-CN');
    }
  };

  const getMediaTypeLabel = (type?: string) => {
    switch (type) {
      case 'movie': return '电影';
      case 'tv': return '电视剧';
      case 'multi': return '全部';
      default: return '';
    }
  };

  return (
    <div className="bg-white rounded-xl shadow-lg border border-slate-200 overflow-hidden">
      {/* Header */}
      <div className="p-4 border-b border-slate-200 bg-slate-50">
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center space-x-2">
            <Clock className="w-5 h-5 text-slate-600" />
            <h3 className="font-semibold text-slate-900">搜索历史</h3>
            {stats && (
              <span className="text-sm text-slate-500">
                ({stats.totalSearches} 条记录)
              </span>
            )}
          </div>
          
          <div className="flex items-center space-x-2">
            <button
              onClick={() => setShowStats(!showStats)}
              className="p-1.5 text-slate-400 hover:text-slate-600 transition-colors"
              title="统计信息"
            >
              <BarChart3 className="w-4 h-4" />
            </button>
            
            <button
              onClick={handleExport}
              className="p-1.5 text-slate-400 hover:text-slate-600 transition-colors"
              title="导出历史"
            >
              <Download className="w-4 h-4" />
            </button>
            
            <label className="p-1.5 text-slate-400 hover:text-slate-600 transition-colors cursor-pointer" title="导入历史">
              <Upload className="w-4 h-4" />
              <input
                type="file"
                accept=".json"
                onChange={handleImport}
                className="hidden"
              />
            </label>
            
            {onClose && (
              <button
                onClick={onClose}
                className="p-1.5 text-slate-400 hover:text-slate-600 transition-colors"
              >
                <X className="w-4 h-4" />
              </button>
            )}
          </div>
        </div>

        {/* Search and Filter */}
        <div className="flex space-x-2">
          <div className="flex-1 relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-slate-400" />
            <input
              type="text"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              placeholder="搜索历史记录..."
              className="w-full pl-9 pr-3 py-2 text-sm border border-slate-200 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
            />
          </div>
          
          <select
            value={filterType}
            onChange={(e) => setFilterType(e.target.value as 'all' | 'movie' | 'tv')}
            className="text-sm border border-slate-200 rounded-lg px-3 py-2 focus:ring-2 focus:ring-primary-500 focus:border-transparent"
          >
            <option value="all">全部类型</option>
            <option value="movie">电影</option>
            <option value="tv">电视剧</option>
          </select>
        </div>
      </div>

      {/* Stats Panel */}
      {showStats && stats && (
        <div className="p-4 bg-primary-50 border-b border-slate-200">
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span className="text-slate-600">总搜索次数:</span>
              <span className="ml-2 font-semibold text-slate-900">{stats.totalSearches}</span>
            </div>
            <div>
              <span className="text-slate-600">不同查询:</span>
              <span className="ml-2 font-semibold text-slate-900">{stats.uniqueQueries}</span>
            </div>
            {stats.mostSearchedQuery && (
              <div className="col-span-2">
                <span className="text-slate-600">最常搜索:</span>
                <span className="ml-2 font-semibold text-primary-600">{stats.mostSearchedQuery}</span>
              </div>
            )}
          </div>
        </div>
      )}

      {/* History List */}
      <div className="max-h-80 overflow-y-auto">
        {history.length === 0 ? (
          <div className="p-8 text-center text-slate-500">
            <Clock className="w-12 h-12 mx-auto mb-3 text-slate-300" />
            <p className="text-sm">
              {searchTerm ? '没有找到匹配的搜索记录' : '暂无搜索历史'}
            </p>
          </div>
        ) : (
          <div className="divide-y divide-slate-100">
            {history.map((item) => (
              <div
                key={item.id}
                onClick={() => handleSearchSelect(item.query)}
                className="p-3 hover:bg-slate-50 cursor-pointer group transition-colors"
              >
                <div className="flex items-center justify-between">
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center space-x-2 mb-1">
                      <span className="font-medium text-slate-900 truncate">
                        {item.query}
                      </span>
                      {item.mediaType && (
                        <span className="px-2 py-0.5 bg-slate-100 text-slate-600 rounded text-xs">
                          {getMediaTypeLabel(item.mediaType)}
                        </span>
                      )}
                    </div>
                    <div className="flex items-center space-x-3 text-xs text-slate-500">
                      <div className="flex items-center space-x-1">
                        <Calendar className="w-3 h-3" />
                        <span>{formatDate(item.timestamp)}</span>
                      </div>
                      {item.resultCount !== undefined && (
                        <span>{item.resultCount} 个结果</span>
                      )}
                    </div>
                  </div>
                  
                  <button
                    onClick={(e) => handleRemoveItem(item.id, e)}
                    className="p-1 text-slate-400 hover:text-red-500 opacity-0 group-hover:opacity-100 transition-all"
                    title="删除此记录"
                  >
                    <X className="w-4 h-4" />
                  </button>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Footer */}
      {history.length > 0 && (
        <div className="p-3 border-t border-slate-200 bg-slate-50">
          <button
            onClick={handleClearAll}
            className="flex items-center space-x-2 text-sm text-red-600 hover:text-red-700 transition-colors"
          >
            <Trash2 className="w-4 h-4" />
            <span>清空所有历史</span>
          </button>
        </div>
      )}
    </div>
  );
};

export default SearchHistory;
