/**
 * 电影搜索组件示例
 * 展示如何使用新创建的Edge Functions API
 */

import React, { useState, useCallback } from 'react'
import { Search, Loader2, Film, Tv, Star, Calendar, Clock } from 'lucide-react'
import { 
  searchMovies, 
  getMovieDetails, 
  formatRuntime, 
  formatReleaseDate, 
  formatRating,
  getPosterUrl,
  type MovieSearchResult,
  type MovieDetails 
} from '../lib/movieApi'

export default function MovieSearchExample() {
  const [query, setQuery] = useState('')
  const [searchResults, setSearchResults] = useState<MovieSearchResult[]>([])
  const [selectedMovie, setSelectedMovie] = useState<MovieDetails | null>(null)
  const [isSearching, setIsSearching] = useState(false)
  const [isLoadingDetails, setIsLoadingDetails] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [searchType, setSearchType] = useState<'movie' | 'tv' | 'multi'>('multi')

  // 搜索电影
  const handleSearch = useCallback(async () => {
    if (!query.trim()) return

    setIsSearching(true)
    setError(null)
    setSelectedMovie(null)

    try {
      const response = await searchMovies({
        query: query.trim(),
        type: searchType,
        page: 1
      })

      setSearchResults(response.data.results)
      
      if (response.cached) {
        console.log('使用了缓存结果')
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : '搜索失败')
      setSearchResults([])
    } finally {
      setIsSearching(false)
    }
  }, [query, searchType])

  // 获取电影详情
  const handleGetDetails = useCallback(async (movie: MovieSearchResult) => {
    setIsLoadingDetails(true)
    setError(null)

    try {
      const response = await getMovieDetails(movie.id, movie.media_type as 'movie' | 'tv')
      setSelectedMovie(response.data)
      
      if (response.cached) {
        console.log('使用了缓存的详情数据')
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : '获取详情失败')
    } finally {
      setIsLoadingDetails(false)
    }
  }, [])

  // 处理回车键搜索
  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSearch()
    }
  }

  return (
    <div className="max-w-6xl mx-auto p-6">
      <h1 className="text-3xl font-bold text-gray-900 mb-8">电影搜索示例</h1>
      
      {/* 搜索区域 */}
      <div className="bg-white rounded-lg shadow-md p-6 mb-8">
        <div className="flex flex-col space-y-4">
          {/* 搜索类型选择 */}
          <div className="flex space-x-4">
            <label className="flex items-center">
              <input
                type="radio"
                value="multi"
                checked={searchType === 'multi'}
                onChange={(e) => setSearchType(e.target.value as 'multi')}
                className="mr-2"
              />
              全部
            </label>
            <label className="flex items-center">
              <input
                type="radio"
                value="movie"
                checked={searchType === 'movie'}
                onChange={(e) => setSearchType(e.target.value as 'movie')}
                className="mr-2"
              />
              <Film className="w-4 h-4 mr-1" />
              电影
            </label>
            <label className="flex items-center">
              <input
                type="radio"
                value="tv"
                checked={searchType === 'tv'}
                onChange={(e) => setSearchType(e.target.value as 'tv')}
                className="mr-2"
              />
              <Tv className="w-4 h-4 mr-1" />
              电视剧
            </label>
          </div>

          {/* 搜索输入框 */}
          <div className="flex space-x-4">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
              <input
                type="text"
                value={query}
                onChange={(e) => setQuery(e.target.value)}
                onKeyPress={handleKeyPress}
                placeholder="输入电影或电视剧名称..."
                className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
            <button
              onClick={handleSearch}
              disabled={isSearching || !query.trim()}
              className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
            >
              {isSearching ? (
                <Loader2 className="w-5 h-5 animate-spin" />
              ) : (
                <Search className="w-5 h-5" />
              )}
              <span>搜索</span>
            </button>
          </div>
        </div>
      </div>

      {/* 错误提示 */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
          <p className="text-red-800">{error}</p>
        </div>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* 搜索结果 */}
        <div>
          <h2 className="text-xl font-semibold text-gray-900 mb-4">
            搜索结果 ({searchResults.length})
          </h2>
          
          <div className="space-y-4 max-h-96 overflow-y-auto">
            {searchResults.map((movie) => (
              <div
                key={movie.id}
                onClick={() => handleGetDetails(movie)}
                className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 cursor-pointer hover:shadow-md transition-shadow"
              >
                <div className="flex space-x-4">
                  <img
                    src={getPosterUrl(movie.poster_url, 'small')}
                    alt={movie.title}
                    className="w-16 h-24 object-cover rounded"
                    onError={(e) => {
                      e.currentTarget.src = '/placeholder-poster.jpg'
                    }}
                  />
                  <div className="flex-1 min-w-0">
                    <h3 className="font-semibold text-gray-900 truncate">
                      {movie.title}
                    </h3>
                    {movie.original_title !== movie.title && (
                      <p className="text-sm text-gray-600 truncate">
                        {movie.original_title}
                      </p>
                    )}
                    <div className="flex items-center space-x-4 mt-2 text-sm text-gray-500">
                      <span className="flex items-center">
                        {movie.media_type === 'movie' ? (
                          <Film className="w-4 h-4 mr-1" />
                        ) : (
                          <Tv className="w-4 h-4 mr-1" />
                        )}
                        {movie.media_type === 'movie' ? '电影' : '电视剧'}
                      </span>
                      <span className="flex items-center">
                        <Star className="w-4 h-4 mr-1 text-yellow-400" />
                        {formatRating(movie.vote_average)}
                      </span>
                      <span className="flex items-center">
                        <Calendar className="w-4 h-4 mr-1" />
                        {movie.release_date ? new Date(movie.release_date).getFullYear() : '未知'}
                      </span>
                    </div>
                    <p className="text-sm text-gray-600 mt-2 line-clamp-2">
                      {movie.overview || '暂无简介'}
                    </p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* 电影详情 */}
        <div>
          <h2 className="text-xl font-semibold text-gray-900 mb-4">详细信息</h2>
          
          {isLoadingDetails ? (
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-8 text-center">
              <Loader2 className="w-8 h-8 animate-spin mx-auto mb-4 text-blue-600" />
              <p className="text-gray-600">加载详情中...</p>
            </div>
          ) : selectedMovie ? (
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <div className="flex space-x-6 mb-6">
                <img
                  src={getPosterUrl(selectedMovie.poster_url, 'medium')}
                  alt={selectedMovie.title}
                  className="w-32 h-48 object-cover rounded-lg"
                  onError={(e) => {
                    e.currentTarget.src = '/placeholder-poster.jpg'
                  }}
                />
                <div className="flex-1">
                  <h3 className="text-2xl font-bold text-gray-900 mb-2">
                    {selectedMovie.title}
                  </h3>
                  {selectedMovie.original_title !== selectedMovie.title && (
                    <p className="text-lg text-gray-600 mb-2">
                      {selectedMovie.original_title}
                    </p>
                  )}
                  {selectedMovie.tagline && (
                    <p className="text-sm text-gray-500 italic mb-4">
                      "{selectedMovie.tagline}"
                    </p>
                  )}
                  
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div className="flex items-center">
                      <Star className="w-4 h-4 mr-2 text-yellow-400" />
                      <span>{formatRating(selectedMovie.vote_average)} ({selectedMovie.vote_count} 评价)</span>
                    </div>
                    <div className="flex items-center">
                      <Calendar className="w-4 h-4 mr-2 text-gray-400" />
                      <span>{formatReleaseDate(selectedMovie.release_date)}</span>
                    </div>
                    {selectedMovie.runtime && (
                      <div className="flex items-center">
                        <Clock className="w-4 h-4 mr-2 text-gray-400" />
                        <span>{formatRuntime(selectedMovie.runtime)}</span>
                      </div>
                    )}
                    <div className="flex items-center">
                      {selectedMovie.media_type === 'movie' ? (
                        <Film className="w-4 h-4 mr-2 text-gray-400" />
                      ) : (
                        <Tv className="w-4 h-4 mr-2 text-gray-400" />
                      )}
                      <span>{selectedMovie.media_type === 'movie' ? '电影' : '电视剧'}</span>
                    </div>
                  </div>
                </div>
              </div>

              {/* 类型标签 */}
              {selectedMovie.genres.length > 0 && (
                <div className="mb-4">
                  <div className="flex flex-wrap gap-2">
                    {selectedMovie.genres.map((genre) => (
                      <span
                        key={genre.id}
                        className="px-3 py-1 bg-blue-100 text-blue-800 text-sm rounded-full"
                      >
                        {genre.name}
                      </span>
                    ))}
                  </div>
                </div>
              )}

              {/* 简介 */}
              <div className="mb-6">
                <h4 className="font-semibold text-gray-900 mb-2">剧情简介</h4>
                <p className="text-gray-700 leading-relaxed">
                  {selectedMovie.overview || '暂无简介'}
                </p>
              </div>

              {/* 主要演员 */}
              {selectedMovie.cast.length > 0 && (
                <div className="mb-6">
                  <h4 className="font-semibold text-gray-900 mb-3">主要演员</h4>
                  <div className="grid grid-cols-2 gap-2">
                    {selectedMovie.cast.slice(0, 6).map((actor) => (
                      <div key={actor.id} className="text-sm">
                        <span className="font-medium">{actor.name}</span>
                        <span className="text-gray-500"> 饰 {actor.character}</span>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* 制作信息 */}
              <div className="text-sm text-gray-600 space-y-1">
                {selectedMovie.crew.directors.length > 0 && (
                  <div>
                    <span className="font-medium">导演: </span>
                    {selectedMovie.crew.directors.map(d => d.name).join(', ')}
                  </div>
                )}
                {selectedMovie.crew.writers.length > 0 && (
                  <div>
                    <span className="font-medium">编剧: </span>
                    {selectedMovie.crew.writers.map(w => w.name).join(', ')}
                  </div>
                )}
                {selectedMovie.production_companies.length > 0 && (
                  <div>
                    <span className="font-medium">制作公司: </span>
                    {selectedMovie.production_companies.map(c => c.name).join(', ')}
                  </div>
                )}
              </div>
            </div>
          ) : (
            <div className="bg-gray-50 rounded-lg border-2 border-dashed border-gray-300 p-8 text-center">
              <p className="text-gray-500">点击左侧搜索结果查看详细信息</p>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
