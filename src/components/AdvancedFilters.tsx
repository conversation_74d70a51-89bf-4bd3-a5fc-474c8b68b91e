import React, { useState } from 'react';
import { 
  Filter, 
  X, 
  ChevronDown, 
  ChevronUp, 
  Calendar, 
  Star, 
  Tag,
  RotateCcw
} from 'lucide-react';
import { 
  getAvailableGenres, 
  getPopularGenres,
  YEAR_RANGE_OPTIONS,
  RATING_RANGE_OPTIONS,
  type YearRange,
  type RatingRange
} from '../lib/genreMapping';

export interface FilterOptions {
  mediaType: 'all' | 'movie' | 'tv';
  genres: number[];
  yearRange: YearRange | null;
  ratingRange: RatingRange | null;
  minVoteCount: number;
  sortBy: 'relevance' | 'year-desc' | 'year-asc' | 'title-asc' | 'title-desc' | 'rating-desc' | 'popularity-desc';
}

interface AdvancedFiltersProps {
  filters: FilterOptions;
  onFiltersChange: (filters: FilterOptions) => void;
  onClose?: () => void;
  resultCount?: number;
}

const AdvancedFilters: React.FC<AdvancedFiltersProps> = ({
  filters,
  onFiltersChange,
  onClose,
  resultCount
}) => {
  const [expandedSections, setExpandedSections] = useState({
    mediaType: true,
    genres: false,
    year: false,
    rating: false,
    sort: true
  });

  const toggleSection = (section: keyof typeof expandedSections) => {
    setExpandedSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }));
  };

  const handleMediaTypeChange = (mediaType: 'all' | 'movie' | 'tv') => {
    onFiltersChange({
      ...filters,
      mediaType,
      genres: [] // 清空类型选择，因为不同媒体类型的类型不同
    });
  };

  const handleGenreToggle = (genreId: number) => {
    const newGenres = filters.genres.includes(genreId)
      ? filters.genres.filter(id => id !== genreId)
      : [...filters.genres, genreId];
    
    onFiltersChange({
      ...filters,
      genres: newGenres
    });
  };

  const handleYearRangeChange = (yearRange: YearRange | null) => {
    onFiltersChange({
      ...filters,
      yearRange
    });
  };

  const handleRatingRangeChange = (ratingRange: RatingRange | null) => {
    onFiltersChange({
      ...filters,
      ratingRange
    });
  };

  const handleSortChange = (sortBy: FilterOptions['sortBy']) => {
    onFiltersChange({
      ...filters,
      sortBy
    });
  };

  const handleReset = () => {
    onFiltersChange({
      mediaType: 'all',
      genres: [],
      yearRange: null,
      ratingRange: null,
      minVoteCount: 0,
      sortBy: 'relevance'
    });
  };

  const getActiveFiltersCount = () => {
    let count = 0;
    if (filters.mediaType !== 'all') count++;
    if (filters.genres.length > 0) count++;
    if (filters.yearRange) count++;
    if (filters.ratingRange) count++;
    if (filters.minVoteCount > 0) count++;
    return count;
  };

  const availableGenres = getAvailableGenres(filters.mediaType === 'all' ? undefined : filters.mediaType);
  const popularGenres = getPopularGenres(filters.mediaType === 'all' ? undefined : filters.mediaType);

  return (
    <div className="bg-white rounded-xl shadow-lg border border-slate-200 overflow-hidden">
      {/* Header */}
      <div className="p-4 border-b border-slate-200 bg-slate-50">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Filter className="w-5 h-5 text-slate-600" />
            <h3 className="font-semibold text-slate-900">高级筛选</h3>
            {getActiveFiltersCount() > 0 && (
              <span className="px-2 py-1 bg-primary-100 text-primary-700 rounded-full text-xs font-medium">
                {getActiveFiltersCount()} 个筛选条件
              </span>
            )}
          </div>
          
          <div className="flex items-center space-x-2">
            <button
              onClick={handleReset}
              className="flex items-center space-x-1 px-3 py-1.5 text-sm text-slate-600 hover:text-slate-800 transition-colors"
            >
              <RotateCcw className="w-4 h-4" />
              <span>重置</span>
            </button>
            
            {onClose && (
              <button
                onClick={onClose}
                className="p-1.5 text-slate-400 hover:text-slate-600 transition-colors"
              >
                <X className="w-4 h-4" />
              </button>
            )}
          </div>
        </div>
        
        {resultCount !== undefined && (
          <p className="text-sm text-slate-500 mt-2">
            当前筛选结果: {resultCount} 项
          </p>
        )}
      </div>

      <div className="max-h-96 overflow-y-auto">
        {/* Media Type Filter */}
        <div className="border-b border-slate-100">
          <button
            onClick={() => toggleSection('mediaType')}
            className="w-full p-4 flex items-center justify-between hover:bg-slate-50 transition-colors"
          >
            <span className="font-medium text-slate-900">媒体类型</span>
            {expandedSections.mediaType ? (
              <ChevronUp className="w-4 h-4 text-slate-500" />
            ) : (
              <ChevronDown className="w-4 h-4 text-slate-500" />
            )}
          </button>
          
          {expandedSections.mediaType && (
            <div className="px-4 pb-4">
              <div className="grid grid-cols-3 gap-2">
                {[
                  { value: 'all', label: '全部' },
                  { value: 'movie', label: '电影' },
                  { value: 'tv', label: '电视剧' }
                ].map(option => (
                  <button
                    key={option.value}
                    onClick={() => handleMediaTypeChange(option.value as 'all' | 'movie' | 'tv')}
                    className={`px-3 py-2 text-sm rounded-lg border transition-colors ${
                      filters.mediaType === option.value
                        ? 'bg-primary-50 border-primary-200 text-primary-700'
                        : 'bg-white border-slate-200 text-slate-700 hover:bg-slate-50'
                    }`}
                  >
                    {option.label}
                  </button>
                ))}
              </div>
            </div>
          )}
        </div>

        {/* Genres Filter */}
        <div className="border-b border-slate-100">
          <button
            onClick={() => toggleSection('genres')}
            className="w-full p-4 flex items-center justify-between hover:bg-slate-50 transition-colors"
          >
            <div className="flex items-center space-x-2">
              <Tag className="w-4 h-4 text-slate-500" />
              <span className="font-medium text-slate-900">类型</span>
              {filters.genres.length > 0 && (
                <span className="px-2 py-0.5 bg-primary-100 text-primary-700 rounded-full text-xs">
                  {filters.genres.length}
                </span>
              )}
            </div>
            {expandedSections.genres ? (
              <ChevronUp className="w-4 h-4 text-slate-500" />
            ) : (
              <ChevronDown className="w-4 h-4 text-slate-500" />
            )}
          </button>
          
          {expandedSections.genres && (
            <div className="px-4 pb-4">
              {/* Popular Genres */}
              <div className="mb-3">
                <p className="text-xs font-medium text-slate-500 uppercase tracking-wide mb-2">热门类型</p>
                <div className="flex flex-wrap gap-2">
                  {popularGenres.slice(0, 6).map(genre => (
                    <button
                      key={genre.id}
                      onClick={() => handleGenreToggle(genre.id)}
                      className={`px-3 py-1.5 text-sm rounded-lg border transition-colors ${
                        filters.genres.includes(genre.id)
                          ? 'bg-primary-50 border-primary-200 text-primary-700'
                          : 'bg-white border-slate-200 text-slate-700 hover:bg-slate-50'
                      }`}
                    >
                      {genre.name}
                    </button>
                  ))}
                </div>
              </div>
              
              {/* All Genres */}
              <div>
                <p className="text-xs font-medium text-slate-500 uppercase tracking-wide mb-2">所有类型</p>
                <div className="grid grid-cols-2 gap-2 max-h-32 overflow-y-auto">
                  {availableGenres.map(genre => (
                    <label
                      key={genre.id}
                      className="flex items-center space-x-2 text-sm cursor-pointer hover:bg-slate-50 p-1 rounded"
                    >
                      <input
                        type="checkbox"
                        checked={filters.genres.includes(genre.id)}
                        onChange={() => handleGenreToggle(genre.id)}
                        className="rounded border-slate-300 text-primary-600 focus:ring-primary-500"
                      />
                      <span className="text-slate-700">{genre.name}</span>
                    </label>
                  ))}
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Year Range Filter */}
        <div className="border-b border-slate-100">
          <button
            onClick={() => toggleSection('year')}
            className="w-full p-4 flex items-center justify-between hover:bg-slate-50 transition-colors"
          >
            <div className="flex items-center space-x-2">
              <Calendar className="w-4 h-4 text-slate-500" />
              <span className="font-medium text-slate-900">年份</span>
            </div>
            {expandedSections.year ? (
              <ChevronUp className="w-4 h-4 text-slate-500" />
            ) : (
              <ChevronDown className="w-4 h-4 text-slate-500" />
            )}
          </button>
          
          {expandedSections.year && (
            <div className="px-4 pb-4">
              <div className="grid grid-cols-2 gap-2">
                {YEAR_RANGE_OPTIONS.map((option, index) => (
                  <button
                    key={index}
                    onClick={() => handleYearRangeChange(option.value)}
                    className={`px-3 py-2 text-sm rounded-lg border transition-colors ${
                      JSON.stringify(filters.yearRange) === JSON.stringify(option.value)
                        ? 'bg-primary-50 border-primary-200 text-primary-700'
                        : 'bg-white border-slate-200 text-slate-700 hover:bg-slate-50'
                    }`}
                  >
                    {option.label}
                  </button>
                ))}
              </div>
            </div>
          )}
        </div>

        {/* Rating Range Filter */}
        <div className="border-b border-slate-100">
          <button
            onClick={() => toggleSection('rating')}
            className="w-full p-4 flex items-center justify-between hover:bg-slate-50 transition-colors"
          >
            <div className="flex items-center space-x-2">
              <Star className="w-4 h-4 text-slate-500" />
              <span className="font-medium text-slate-900">评分</span>
            </div>
            {expandedSections.rating ? (
              <ChevronUp className="w-4 h-4 text-slate-500" />
            ) : (
              <ChevronDown className="w-4 h-4 text-slate-500" />
            )}
          </button>
          
          {expandedSections.rating && (
            <div className="px-4 pb-4">
              <div className="grid grid-cols-2 gap-2">
                {RATING_RANGE_OPTIONS.map((option, index) => (
                  <button
                    key={index}
                    onClick={() => handleRatingRangeChange(option.value)}
                    className={`px-3 py-2 text-sm rounded-lg border transition-colors ${
                      JSON.stringify(filters.ratingRange) === JSON.stringify(option.value)
                        ? 'bg-primary-50 border-primary-200 text-primary-700'
                        : 'bg-white border-slate-200 text-slate-700 hover:bg-slate-50'
                    }`}
                  >
                    {option.label}
                  </button>
                ))}
              </div>
            </div>
          )}
        </div>

        {/* Sort Options */}
        <div>
          <button
            onClick={() => toggleSection('sort')}
            className="w-full p-4 flex items-center justify-between hover:bg-slate-50 transition-colors"
          >
            <span className="font-medium text-slate-900">排序方式</span>
            {expandedSections.sort ? (
              <ChevronUp className="w-4 h-4 text-slate-500" />
            ) : (
              <ChevronDown className="w-4 h-4 text-slate-500" />
            )}
          </button>
          
          {expandedSections.sort && (
            <div className="px-4 pb-4">
              <div className="space-y-2">
                {[
                  { value: 'relevance', label: '相关性' },
                  { value: 'rating-desc', label: '评分 (高到低)' },
                  { value: 'popularity-desc', label: '热度 (高到低)' },
                  { value: 'year-desc', label: '年份 (新到旧)' },
                  { value: 'year-asc', label: '年份 (旧到新)' },
                  { value: 'title-asc', label: '标题 (A-Z)' },
                  { value: 'title-desc', label: '标题 (Z-A)' }
                ].map(option => (
                  <label
                    key={option.value}
                    className="flex items-center space-x-2 text-sm cursor-pointer hover:bg-slate-50 p-2 rounded"
                  >
                    <input
                      type="radio"
                      name="sortBy"
                      value={option.value}
                      checked={filters.sortBy === option.value}
                      onChange={() => handleSortChange(option.value as FilterOptions['sortBy'])}
                      className="text-primary-600 focus:ring-primary-500"
                    />
                    <span className="text-slate-700">{option.label}</span>
                  </label>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default AdvancedFilters;
