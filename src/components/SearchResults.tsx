import React, { useState, useMemo, useRef, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Star, Calendar, PlayCircle, Filter, SortAsc, SortDesc, Grid, List, Search, Settings, Layers } from 'lucide-react';
import AdvancedFilters, { type FilterOptions } from './AdvancedFilters';
import Pagination from './Pagination';
import VirtualScrollList, { VirtualGrid } from './VirtualScrollList';
import { usePagination } from '../hooks/usePagination';
import {
  mapGenreIds,
  filterByYearRange,
  filterByRatingRange,
  hasGenre,
  type YearRange,
  type RatingRange
} from '../lib/genreMapping';

interface SearchResult {
  media_id: string;
  title: string;
  original_title: string;
  year: number;
  poster_url: string;
  genres: string[];
  genre_ids?: number[];
  vote_average?: number;
  vote_count?: number;
  popularity?: number;
  media_type?: 'movie' | 'tv';
  release_date?: string | null;
}

interface SearchResultsProps {
  results: SearchResult[];
  query: string;
  isLoading?: boolean;
}

type SortOption = 'relevance' | 'year-desc' | 'year-asc' | 'title-asc' | 'title-desc' | 'rating-desc' | 'popularity-desc';
type ViewMode = 'grid' | 'list' | 'virtual-grid' | 'virtual-list';

const SearchResults: React.FC<SearchResultsProps> = ({ results, query, isLoading = false }) => {
  const navigate = useNavigate();
  const [viewMode, setViewMode] = useState<ViewMode>('grid');
  const [showAdvancedFilters, setShowAdvancedFilters] = useState(false);
  const [enableVirtualization, setEnableVirtualization] = useState(false);
  const containerRef = useRef<HTMLDivElement>(null);
  const [containerDimensions, setContainerDimensions] = useState({ width: 0, height: 600 });

  const [filters, setFilters] = useState<FilterOptions>({
    mediaType: 'all',
    genres: [],
    yearRange: null,
    ratingRange: null,
    minVoteCount: 0,
    sortBy: 'relevance'
  });

  // 分页管理
  const pagination = usePagination({
    initialPage: 1,
    initialItemsPerPage: 20,
    totalItems: 0
  });

  const handleItemClick = (mediaId: string) => {
    navigate(`/details/${mediaId}`);
  };

  // 监听容器尺寸变化
  useEffect(() => {
    const updateDimensions = () => {
      if (containerRef.current) {
        const { clientWidth, clientHeight } = containerRef.current;
        setContainerDimensions({
          width: clientWidth,
          height: Math.max(clientHeight, 600)
        });
      }
    };

    updateDimensions();

    const resizeObserver = new ResizeObserver(updateDimensions);
    if (containerRef.current) {
      resizeObserver.observe(containerRef.current);
    }

    return () => {
      resizeObserver.disconnect();
    };
  }, []);

  // 自动启用虚拟化（当结果数量超过阈值时）
  useEffect(() => {
    const shouldEnableVirtualization = results.length > 100;
    setEnableVirtualization(shouldEnableVirtualization);

    if (shouldEnableVirtualization && (viewMode === 'grid' || viewMode === 'list')) {
      setViewMode(viewMode === 'grid' ? 'virtual-grid' : 'virtual-list');
    }
  }, [results.length, viewMode]);

  // 高级过滤和排序逻辑
  const filteredAndSortedResults = useMemo(() => {
    let filtered = results.map(item => ({
      ...item,
      // 确保有genres数组，如果没有则从genre_ids映射
      genres: item.genres.length > 0 ? item.genres : mapGenreIds(item.genre_ids || [], item.media_type)
    }));

    // 按媒体类型过滤
    if (filters.mediaType !== 'all') {
      filtered = filtered.filter(item => item.media_type === filters.mediaType);
    }

    // 按类型过滤
    if (filters.genres.length > 0) {
      filtered = filtered.filter(item => {
        const itemGenreIds = item.genre_ids || [];
        return filters.genres.some(genreId => hasGenre(itemGenreIds, genreId));
      });
    }

    // 按年份范围过滤
    if (filters.yearRange) {
      filtered = filtered.filter(item =>
        filterByYearRange(item.release_date || null, filters.yearRange!)
      );
    }

    // 按评分范围过滤
    if (filters.ratingRange) {
      filtered = filtered.filter(item =>
        filterByRatingRange(item.vote_average || 0, filters.ratingRange!)
      );
    }

    // 按最小投票数过滤
    if (filters.minVoteCount > 0) {
      filtered = filtered.filter(item => (item.vote_count || 0) >= filters.minVoteCount);
    }

    // 排序
    const sorted = [...filtered].sort((a, b) => {
      switch (filters.sortBy) {
        case 'year-desc':
          return (b.year || 0) - (a.year || 0);
        case 'year-asc':
          return (a.year || 0) - (b.year || 0);
        case 'title-asc':
          return a.title.localeCompare(b.title);
        case 'title-desc':
          return b.title.localeCompare(a.title);
        case 'rating-desc':
          return (b.vote_average || 0) - (a.vote_average || 0);
        case 'popularity-desc':
          return (b.popularity || 0) - (a.popularity || 0);
        case 'relevance':
        default:
          return 0; // 保持原始顺序（API返回的相关性排序）
      }
    });

    return sorted;
  }, [results, filters]);

  // 更新分页总数
  useEffect(() => {
    pagination.actions.setTotalItems(filteredAndSortedResults.length);
  }, [filteredAndSortedResults.length, pagination.actions]);

  // 获取当前页的结果（仅在非虚拟化模式下使用）
  const paginatedResults = useMemo(() => {
    if (viewMode.startsWith('virtual')) {
      return filteredAndSortedResults; // 虚拟化模式显示所有结果
    }
    return pagination.getPageItems(filteredAndSortedResults);
  }, [filteredAndSortedResults, pagination, viewMode]);

  // 渲染网格项目
  const renderGridItem = (item: SearchResult, index: number) => (
    <div
      key={item.media_id}
      onClick={() => handleItemClick(item.media_id)}
      className="group bg-white rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-300 overflow-hidden cursor-pointer border border-slate-100 hover:border-primary-200 animate-slide-up"
    >
      <div className="relative h-80 overflow-hidden">
        <img
          src={item.poster_url}
          alt={item.title}
          className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-300"
          onError={(e) => {
            e.currentTarget.src = 'https://images.unsplash.com/photo-1478720568477-b0ac8e6c4a17';
          }}
        />
        <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity">
          <div className="absolute bottom-4 left-4 right-4">
            <button className="flex items-center justify-center w-full py-3 bg-primary-500/90 backdrop-blur-sm text-white rounded-xl hover:bg-primary-600/90 transition-all font-medium">
              <PlayCircle className="w-5 h-5 mr-2" />
              生成解说文案
            </button>
          </div>
        </div>

        {/* Badges */}
        <div className="absolute top-4 right-4 space-y-2">
          {item.year && (
            <div className="flex items-center space-x-1 px-2 py-1 bg-black/60 backdrop-blur-sm text-white rounded-lg text-sm">
              <Calendar className="w-3 h-3" />
              <span>{item.year}</span>
            </div>
          )}
          {item.vote_average && item.vote_average > 0 && (
            <div className="flex items-center space-x-1 px-2 py-1 bg-black/60 backdrop-blur-sm text-white rounded-lg text-sm">
              <Star className="w-3 h-3 fill-yellow-400 text-yellow-400" />
              <span>{item.vote_average.toFixed(1)}</span>
            </div>
          )}
        </div>

        {/* Media Type Badge */}
        {item.media_type && (
          <div className="absolute top-4 left-4">
            <span className="px-2 py-1 bg-primary-500/90 backdrop-blur-sm text-white rounded-lg text-xs font-medium">
              {item.media_type === 'movie' ? '电影' : '电视剧'}
            </span>
          </div>
        )}
      </div>

      <div className="p-6">
        <h3 className="font-bold text-slate-900 mb-1 group-hover:text-primary-600 transition-colors line-clamp-1">
          {item.title}
        </h3>

        {item.original_title !== item.title && (
          <p className="text-sm text-slate-500 mb-3 line-clamp-1">
            {item.original_title}
          </p>
        )}

        <div className="flex flex-wrap gap-2">
          {item.genres.slice(0, 3).map((genre) => (
            <span
              key={genre}
              className="px-2 py-1 bg-slate-100 text-slate-600 rounded-lg text-xs font-medium"
            >
              {genre}
            </span>
          ))}
        </div>
      </div>
    </div>
  );

  // 渲染列表项目
  const renderListItem = (item: SearchResult, index: number) => (
    <div
      key={item.media_id}
      onClick={() => handleItemClick(item.media_id)}
      className="group bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-200 overflow-hidden cursor-pointer border border-slate-100 hover:border-primary-200"
    >
      <div className="flex p-4 space-x-4">
        <div className="relative w-24 h-36 flex-shrink-0 overflow-hidden rounded-lg">
          <img
            src={item.poster_url}
            alt={item.title}
            className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
            onError={(e) => {
              e.currentTarget.src = 'https://images.unsplash.com/photo-1478720568477-b0ac8e6c4a17';
            }}
          />
          {item.media_type && (
            <div className="absolute top-2 left-2">
              <span className="px-1.5 py-0.5 bg-primary-500/90 backdrop-blur-sm text-white rounded text-xs font-medium">
                {item.media_type === 'movie' ? '电影' : '剧集'}
              </span>
            </div>
          )}
        </div>

        <div className="flex-1 min-w-0">
          <div className="flex items-start justify-between mb-2">
            <div className="min-w-0 flex-1">
              <h3 className="font-bold text-slate-900 group-hover:text-primary-600 transition-colors truncate">
                {item.title}
              </h3>
              {item.original_title !== item.title && (
                <p className="text-sm text-slate-500 truncate">
                  {item.original_title}
                </p>
              )}
            </div>

            <div className="flex items-center space-x-3 ml-4">
              {item.year && (
                <div className="flex items-center space-x-1 text-slate-500">
                  <Calendar className="w-4 h-4" />
                  <span className="text-sm">{item.year}</span>
                </div>
              )}
              {item.vote_average && item.vote_average > 0 && (
                <div className="flex items-center space-x-1 text-slate-500">
                  <Star className="w-4 h-4 fill-yellow-400 text-yellow-400" />
                  <span className="text-sm">{item.vote_average.toFixed(1)}</span>
                </div>
              )}
            </div>
          </div>

          <div className="flex flex-wrap gap-2 mb-3">
            {item.genres.slice(0, 4).map((genre) => (
              <span
                key={genre}
                className="px-2 py-1 bg-slate-100 text-slate-600 rounded text-xs font-medium"
              >
                {genre}
              </span>
            ))}
          </div>

          <button className="flex items-center space-x-2 px-4 py-2 bg-primary-50 hover:bg-primary-100 text-primary-600 rounded-lg transition-colors text-sm font-medium">
            <PlayCircle className="w-4 h-4" />
            <span>生成解说文案</span>
          </button>
        </div>
      </div>
    </div>
  );

  if (isLoading) {
    return (
      <section className="py-16 px-4 sm:px-6 lg:px-8 bg-white">
        <div className="max-w-7xl mx-auto">
          <div className="flex items-center justify-center py-20">
            <div className="text-center">
              <div className="w-12 h-12 border-4 border-primary-200 border-t-primary-600 rounded-full animate-spin mx-auto mb-4"></div>
              <p className="text-slate-600">搜索中...</p>
            </div>
          </div>
        </div>
      </section>
    );
  }

  return (
    <section className="py-16 px-4 sm:px-6 lg:px-8 bg-white">
      <div className="max-w-7xl mx-auto">
        {/* Header with Search Info */}
        <div className="mb-8">
          <div className="flex items-center justify-between mb-4">
            <div>
              <h2 className="text-2xl font-bold text-slate-900 mb-2">
                搜索结果
              </h2>
              <p className="text-slate-600">
                为 <span className="font-semibold text-primary-600">"{query}"</span> 找到 {filteredAndSortedResults.length} 个结果
                {filteredAndSortedResults.length !== results.length && (
                  <span className="text-slate-500"> (共 {results.length} 个)</span>
                )}
              </p>
            </div>

            {/* View Mode Toggle */}
            <div className="flex items-center space-x-2">
              <button
                onClick={() => setViewMode(enableVirtualization ? 'virtual-grid' : 'grid')}
                className={`p-2 rounded-lg transition-colors ${
                  viewMode === 'grid' || viewMode === 'virtual-grid'
                    ? 'bg-primary-100 text-primary-600'
                    : 'text-slate-400 hover:text-slate-600'
                }`}
                title={enableVirtualization ? '虚拟网格视图' : '网格视图'}
              >
                <Grid className="w-5 h-5" />
                {enableVirtualization && viewMode === 'virtual-grid' && (
                  <Layers className="w-3 h-3 absolute -top-1 -right-1" />
                )}
              </button>
              <button
                onClick={() => setViewMode(enableVirtualization ? 'virtual-list' : 'list')}
                className={`p-2 rounded-lg transition-colors ${
                  viewMode === 'list' || viewMode === 'virtual-list'
                    ? 'bg-primary-100 text-primary-600'
                    : 'text-slate-400 hover:text-slate-600'
                }`}
                title={enableVirtualization ? '虚拟列表视图' : '列表视图'}
              >
                <List className="w-5 h-5" />
                {enableVirtualization && viewMode === 'virtual-list' && (
                  <Layers className="w-3 h-3 absolute -top-1 -right-1" />
                )}
              </button>
            </div>
          </div>

          {/* Quick Filters and Sort */}
          <div className="flex flex-wrap items-center gap-4 p-4 bg-slate-50 rounded-xl">
            {/* Quick Type Filter */}
            <div className="flex items-center space-x-2">
              <Filter className="w-4 h-4 text-slate-500" />
              <span className="text-sm font-medium text-slate-700">类型:</span>
              <select
                value={filters.mediaType}
                onChange={(e) => setFilters(prev => ({ ...prev, mediaType: e.target.value as 'all' | 'movie' | 'tv' }))}
                className="text-sm border border-slate-200 rounded-lg px-3 py-1 focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              >
                <option value="all">全部</option>
                <option value="movie">电影</option>
                <option value="tv">电视剧</option>
              </select>
            </div>

            {/* Quick Sort Options */}
            <div className="flex items-center space-x-2">
              <SortAsc className="w-4 h-4 text-slate-500" />
              <span className="text-sm font-medium text-slate-700">排序:</span>
              <select
                value={filters.sortBy}
                onChange={(e) => setFilters(prev => ({ ...prev, sortBy: e.target.value as FilterOptions['sortBy'] }))}
                className="text-sm border border-slate-200 rounded-lg px-3 py-1 focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              >
                <option value="relevance">相关性</option>
                <option value="rating-desc">评分 (高到低)</option>
                <option value="popularity-desc">热度 (高到低)</option>
                <option value="year-desc">年份 (新到旧)</option>
                <option value="year-asc">年份 (旧到新)</option>
                <option value="title-asc">标题 (A-Z)</option>
                <option value="title-desc">标题 (Z-A)</option>
              </select>
            </div>

            {/* Advanced Filters Button */}
            <button
              onClick={() => setShowAdvancedFilters(!showAdvancedFilters)}
              className={`flex items-center space-x-2 px-3 py-1.5 text-sm rounded-lg border transition-colors ${
                showAdvancedFilters || filters.genres.length > 0 || filters.yearRange || filters.ratingRange
                  ? 'bg-primary-50 border-primary-200 text-primary-700'
                  : 'bg-white border-slate-200 text-slate-700 hover:bg-slate-50'
              }`}
            >
              <Settings className="w-4 h-4" />
              <span>高级筛选</span>
              {(filters.genres.length > 0 || filters.yearRange || filters.ratingRange) && (
                <span className="px-1.5 py-0.5 bg-primary-100 text-primary-700 rounded-full text-xs">
                  {[
                    filters.genres.length > 0 ? 1 : 0,
                    filters.yearRange ? 1 : 0,
                    filters.ratingRange ? 1 : 0
                  ].reduce((a, b) => a + b, 0)}
                </span>
              )}
            </button>

            {/* Results Count */}
            <div className="ml-auto text-sm text-slate-500">
              显示 {filteredAndSortedResults.length} / {results.length} 个结果
            </div>
          </div>

          {/* Advanced Filters Panel */}
          {showAdvancedFilters && (
            <div className="mb-6">
              <AdvancedFilters
                filters={filters}
                onFiltersChange={setFilters}
                onClose={() => setShowAdvancedFilters(false)}
                resultCount={filteredAndSortedResults.length}
              />
            </div>
          )}
        </div>

        {/* Results Container */}
        <div ref={containerRef} className="min-h-[600px]">
          {filteredAndSortedResults.length === 0 ? (
            <div className="text-center py-20">
              <Search className="w-16 h-16 text-slate-300 mx-auto mb-4" />
              <h3 className="text-xl font-semibold text-slate-600 mb-2">没有找到相关结果</h3>
              <p className="text-slate-500">
                尝试使用不同的关键词或调整筛选条件
              </p>
            </div>
          ) : (
            <>
              {/* Performance Info */}
              {enableVirtualization && (
                <div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                  <div className="flex items-center space-x-2 text-sm text-blue-700">
                    <Layers className="w-4 h-4" />
                    <span>
                      已启用虚拟滚动优化 ({filteredAndSortedResults.length} 项结果)
                    </span>
                  </div>
                </div>
              )}

              {/* Virtual Grid View */}
              {viewMode === 'virtual-grid' && (
                <VirtualGrid
                  items={filteredAndSortedResults}
                  itemWidth={280}
                  itemHeight={420}
                  containerWidth={containerDimensions.width}
                  containerHeight={containerDimensions.height}
                  renderItem={renderGridItem}
                  gap={24}
                  className="virtual-grid-container"
                />
              )}

              {/* Virtual List View */}
              {viewMode === 'virtual-list' && (
                <VirtualScrollList
                  items={filteredAndSortedResults}
                  itemHeight={160}
                  containerHeight={containerDimensions.height}
                  renderItem={renderListItem}
                  className="virtual-list-container"
                />
              )}

              {/* Regular Grid View */}
              {viewMode === 'grid' && (
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                  {paginatedResults.map((item, index) => renderGridItem(item, index))}
                </div>
              )}

              {/* Regular List View */}
              {viewMode === 'list' && (
                <div className="space-y-4">
                  {paginatedResults.map((item, index) => renderListItem(item, index))}
                </div>
              )}
            </>
          )}
        </div>

        {/* Pagination (only for non-virtual views) */}
        {!viewMode.startsWith('virtual') && filteredAndSortedResults.length > 0 && (
          <div className="mt-8">
            <Pagination
              state={pagination.state}
              actions={pagination.actions}
              showItemsPerPage={true}
              showPageInfo={true}
            />
          </div>
        )}

        {/* Performance Tips */}
        {filteredAndSortedResults.length > 50 && !enableVirtualization && (
          <div className="mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
            <div className="flex items-start space-x-3">
              <div className="flex-shrink-0">
                <div className="w-5 h-5 bg-yellow-400 rounded-full flex items-center justify-center">
                  <span className="text-white text-xs font-bold">!</span>
                </div>
              </div>
              <div>
                <h4 className="text-sm font-medium text-yellow-800">性能提示</h4>
                <p className="text-sm text-yellow-700 mt-1">
                  当前有 {filteredAndSortedResults.length} 个结果。为了更好的性能，建议使用虚拟滚动模式或添加更多筛选条件。
                </p>
                <button
                  onClick={() => {
                    setEnableVirtualization(true);
                    setViewMode(viewMode === 'list' ? 'virtual-list' : 'virtual-grid');
                  }}
                  className="mt-2 text-sm text-yellow-800 underline hover:text-yellow-900"
                >
                  启用虚拟滚动
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </section>
  );
};

export default SearchResults;