import React, { useState } from 'react';
import { Calendar, Clock, Star, User, Users, Sparkles, Lock } from 'lucide-react';
import { useAuth } from '../contexts/AuthContext';
import { generateCommentary } from '../lib/movieApi';

interface MovieDetailsProps {
  movie: any;
  onScriptGenerated: (script: any) => void;
}

const MovieDetails: React.FC<MovieDetailsProps> = ({ movie, onScriptGenerated }) => {
  const [isGenerating, setIsGenerating] = useState(false);
  const [showStyleModal, setShowStyleModal] = useState(false);
  const [showUpgradePrompt, setShowUpgradePrompt] = useState(false);
  
  const { user, profile, canCreateProject, incrementUsage } = useAuth();

  const styles = [
    {
      id: 'passionate',
      name: '激情吐槽',
      description: '幽默风趣，槽点满满',
      color: 'from-error-500 to-warning-500',
      icon: '🔥'
    },
    {
      id: 'analytical',
      name: '深度解析',
      description: '专业分析，见解独到',
      color: 'from-primary-500 to-secondary-500',
      icon: '🔍'
    },
    {
      id: 'suspense',
      name: '悬疑故事',
      description: '引人入胜，扣人心弦',
      color: 'from-slate-700 to-slate-900',
      icon: '🕵️'
    },
    {
      id: 'emotional',
      name: '情感共鸣',
      description: '温暖治愈，触动人心',
      color: 'from-accent-500 to-success-500',
      icon: '💖'
    }
  ];

  const handleGenerateScript = () => {
    if (!user) {
      // Show login prompt
      return;
    }

    if (!canCreateProject()) {
      setShowUpgradePrompt(true);
      return;
    }

    setShowStyleModal(true);
  };

  const handleStyleSelect = async (styleId: string) => {
    console.log('🎯 用户选择解说风格:', styleId);
    setShowStyleModal(false);
    setIsGenerating(true);

    try {
      console.log('📊 开始增加使用次数...');
      // Increment usage count
      await incrementUsage();
      console.log('✅ 使用次数增加成功');

      console.log('🎬 开始生成解说文案:', {
        movieId: movie.media_id,
        movieTitle: movie.title,
        style: styleId,
        movieData: movie
      });

      // 使用新的API函数生成解说文案
      const result = await generateCommentary({
        movie_id: movie.media_id,
        movie_title: movie.title,
        movie_data: movie,
        style: styleId
      });

      console.log('✅ 解说文案生成成功:', result);

      if (result.data) {
        onScriptGenerated(result.data);
      } else {
        throw new Error('返回数据格式错误');
      }

      setIsGenerating(false);
    } catch (error) {
      console.error('❌ 生成解说文案失败:', error);
      setIsGenerating(false);

      // 显示错误提示
      const errorMessage = error instanceof Error ? error.message : '生成解说文案失败，请稍后重试';
      console.error('错误详情:', errorMessage);
      alert(errorMessage);
    }
  };

  const getButtonContent = () => {
    if (!user) {
      return (
        <>
          <Lock className="w-5 h-5" />
          <span>登录后开始创作</span>
        </>
      );
    }

    if (!canCreateProject()) {
      return (
        <>
          <Lock className="w-5 h-5" />
          <span>升级Pro解锁无限创作</span>
        </>
      );
    }

    if (isGenerating) {
      return (
        <>
          <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
          <span>AI编剧中...</span>
        </>
      );
    }

    return (
      <>
        <Sparkles className="w-5 h-5" />
        <span>生成解说文案</span>
      </>
    );
  };

  return (
    <div className="bg-white rounded-2xl shadow-lg overflow-hidden">
      {/* Backdrop */}
      <div className="relative h-48 overflow-hidden">
        <img
          src={movie.backdrop_url}
          alt={movie.title}
          className="w-full h-full object-cover"
        />
        <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent"></div>
      </div>

      <div className="p-6">
        {/* Poster and Basic Info */}
        <div className="flex space-x-4 mb-6">
          <img
            src={movie.poster_url}
            alt={movie.title}
            className="w-24 h-36 object-cover rounded-lg shadow-lg flex-shrink-0"
          />
          <div className="flex-1">
            <h1 className="text-xl font-bold text-slate-900 mb-1">{movie.title}</h1>
            {movie.original_title !== movie.title && (
              <p className="text-sm text-slate-500 mb-2">{movie.original_title}</p>
            )}
            <div className="flex items-center space-x-4 text-sm text-slate-600 mb-3">
              <div className="flex items-center space-x-1">
                <Calendar className="w-4 h-4" />
                <span>{movie.year}</span>
              </div>
              <div className="flex items-center space-x-1">
                <Clock className="w-4 h-4" />
                <span>{movie.duration}</span>
              </div>
              <div className="flex items-center space-x-1">
                <Star className="w-4 h-4 text-yellow-400 fill-current" />
                <span>{movie.rating}</span>
              </div>
            </div>
            <div className="flex flex-wrap gap-2">
              {movie.genres.map((genre: string) => (
                <span
                  key={genre}
                  className="px-2 py-1 bg-primary-50 text-primary-600 rounded text-xs font-medium"
                >
                  {genre}
                </span>
              ))}
            </div>
          </div>
        </div>

        {/* Director and Cast */}
        <div className="space-y-4 mb-6">
          <div className="flex items-center space-x-2">
            <User className="w-4 h-4 text-slate-400" />
            <span className="text-sm text-slate-600">导演:</span>
            <span className="text-sm font-medium text-slate-900">{movie.director}</span>
          </div>
          <div className="flex items-start space-x-2">
            <Users className="w-4 h-4 text-slate-400 mt-0.5 flex-shrink-0" />
            <div>
              <span className="text-sm text-slate-600">主演:</span>
              <div className="flex flex-wrap gap-1 mt-1">
                {movie.actors.map((actor: string) => (
                  <span
                    key={actor}
                    className="text-sm text-slate-700 bg-slate-50 px-2 py-1 rounded"
                  >
                    {actor}
                  </span>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* Plot Summary */}
        <div className="mb-8">
          <h3 className="text-sm font-semibold text-slate-900 mb-2">剧情简介</h3>
          <p className="text-sm text-slate-600 leading-relaxed">{movie.plot_summary}</p>
        </div>

        {/* Usage Info for Free Users */}
        {user && profile?.subscription_tier === 'free' && (
          <div className="mb-6 p-4 bg-slate-50 rounded-xl">
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm text-slate-600">本月剩余额度</span>
              <span className="text-sm font-medium text-slate-900">
                {Math.max(0, profile.monthly_limit - profile.usage_count)}/{profile.monthly_limit}
              </span>
            </div>
            <div className="w-full bg-slate-200 rounded-full h-2">
              <div 
                className="bg-primary-500 h-2 rounded-full transition-all"
                style={{ width: `${(profile.usage_count / profile.monthly_limit) * 100}%` }}
              ></div>
            </div>
          </div>
        )}

        {/* Generate Script Button */}
        <button
          onClick={handleGenerateScript}
          disabled={isGenerating}
          className={`w-full py-4 rounded-xl transition-all font-medium shadow-lg hover:shadow-xl flex items-center justify-center space-x-2 ${
            !user || !canCreateProject()
              ? 'bg-gradient-to-r from-slate-400 to-slate-500 text-white cursor-not-allowed'
              : 'bg-gradient-to-r from-primary-500 to-primary-600 text-white hover:from-primary-600 hover:to-primary-700'
          } ${isGenerating ? 'opacity-50 cursor-not-allowed' : ''}`}
        >
          {getButtonContent()}
        </button>
      </div>

      {/* Style Selection Modal */}
      {showStyleModal && (
        <div className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/50 backdrop-blur-sm">
          <div className="bg-white rounded-2xl p-6 max-w-2xl w-full max-h-[80vh] overflow-y-auto">
            <h3 className="text-xl font-bold text-slate-900 mb-4">选择解说风格</h3>
            <p className="text-slate-600 mb-6">不同的风格会影响文案的语调和表达方式</p>
            
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-6">
              {styles.map((style) => (
                <button
                  key={style.id}
                  onClick={() => handleStyleSelect(style.id)}
                  className="p-4 border-2 border-slate-200 rounded-xl hover:border-primary-500 transition-all text-left group"
                >
                  <div className="flex items-center space-x-3 mb-2">
                    <div className={`w-10 h-10 bg-gradient-to-r ${style.color} rounded-lg flex items-center justify-center text-white text-lg`}>
                      {style.icon}
                    </div>
                    <h4 className="font-semibold text-slate-900 group-hover:text-primary-600">
                      {style.name}
                    </h4>
                  </div>
                  <p className="text-sm text-slate-600">{style.description}</p>
                </button>
              ))}
            </div>

            <div className="flex space-x-3">
              <button
                onClick={() => setShowStyleModal(false)}
                className="flex-1 py-3 border border-slate-300 text-slate-700 rounded-xl hover:bg-slate-50 transition-all"
              >
                取消
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Upgrade Prompt Modal */}
      {showUpgradePrompt && (
        <div className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/50 backdrop-blur-sm">
          <div className="bg-white rounded-2xl p-6 max-w-md w-full">
            <div className="text-center mb-6">
              <div className="w-16 h-16 bg-gradient-to-r from-warning-500 to-error-500 rounded-2xl flex items-center justify-center mx-auto mb-4">
                <Lock className="w-8 h-8 text-white" />
              </div>
              <h3 className="text-xl font-bold text-slate-900 mb-2">已达到免费额度上限</h3>
              <p className="text-slate-600">
                您本月的免费创作次数已用完，升级Pro版即可解锁无限创作
              </p>
            </div>

            <div className="space-y-3">
              <button className="w-full py-3 bg-gradient-to-r from-primary-500 to-primary-600 text-white rounded-xl hover:from-primary-600 hover:to-primary-700 transition-all font-medium">
                立即升级Pro版
              </button>
              <button
                onClick={() => setShowUpgradePrompt(false)}
                className="w-full py-3 border border-slate-300 text-slate-700 rounded-xl hover:bg-slate-50 transition-all"
              >
                稍后再说
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default MovieDetails;