import React, { useState, useEffect, useRef, useCallback } from 'react';
import { <PERSON>, Sparkles, TrendingUp, Clock, X, AlertCircle, History } from 'lucide-react';
import { searchHistoryManager } from '../lib/searchHistory';
import SearchHistory from './SearchHistory';

interface SearchSectionProps {
  onSearch: (query: string) => void;
  isSearching: boolean;
  error?: string | null;
  onClearError?: () => void;
}

interface SearchSuggestion {
  id: string;
  title: string;
  type: 'movie' | 'tv';
  year?: number;
}

const SearchSection: React.FC<SearchSectionProps> = ({
  onSearch,
  isSearching,
  error,
  onClearError
}) => {
  const [query, setQuery] = useState('');
  const [suggestions, setSuggestions] = useState<SearchSuggestion[]>([]);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [searchHistory, setSearchHistory] = useState<string[]>([]);
  const [showHistory, setShowHistory] = useState(false);
  const [showFullHistory, setShowFullHistory] = useState(false);
  const [isLoadingSuggestions, setIsLoadingSuggestions] = useState(false);

  const searchInputRef = useRef<HTMLInputElement>(null);
  const suggestionsRef = useRef<HTMLDivElement>(null);
  const debounceRef = useRef<NodeJS.Timeout>();

  // 从搜索历史管理器加载搜索历史
  useEffect(() => {
    const loadHistory = () => {
      const recentQueries = searchHistoryManager.getRecentQueries(10);
      setSearchHistory(recentQueries);
    };

    loadHistory();

    // 监听存储变化（如果其他标签页修改了历史）
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === 'narrato-search-history') {
        loadHistory();
      }
    };

    window.addEventListener('storage', handleStorageChange);
    return () => window.removeEventListener('storage', handleStorageChange);
  }, []);

  // 保存搜索历史
  const saveSearchHistory = useCallback((newQuery: string, resultCount?: number) => {
    searchHistoryManager.addSearch(newQuery, resultCount, 'multi');
    // 更新本地状态
    const updatedHistory = searchHistoryManager.getRecentQueries(10);
    setSearchHistory(updatedHistory);
  }, []);

  // 防抖搜索建议
  const debouncedGetSuggestions = useCallback(async (searchQuery: string) => {
    if (debounceRef.current) {
      clearTimeout(debounceRef.current);
    }

    debounceRef.current = setTimeout(async () => {
      if (searchQuery.trim().length < 2) {
        setSuggestions([]);
        setShowSuggestions(false);
        return;
      }

      setIsLoadingSuggestions(true);
      try {
        // 这里可以调用实际的搜索建议API
        // 暂时使用模拟数据
        const mockSuggestions: SearchSuggestion[] = [
          { id: '1', title: `${searchQuery}相关电影1`, type: 'movie', year: 2023 },
          { id: '2', title: `${searchQuery}相关电影2`, type: 'movie', year: 2022 },
          { id: '3', title: `${searchQuery}相关剧集`, type: 'tv', year: 2023 },
        ];

        setSuggestions(mockSuggestions);
        setShowSuggestions(true);
      } catch (error) {
        console.error('Failed to get suggestions:', error);
        setSuggestions([]);
      } finally {
        setIsLoadingSuggestions(false);
      }
    }, 300);
  }, []);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setQuery(value);

    // 清除错误状态
    if (error && onClearError) {
      onClearError();
    }

    // 如果输入为空，显示搜索历史
    if (value.trim() === '') {
      setShowSuggestions(false);
      setShowHistory(searchHistory.length > 0);
    } else {
      setShowHistory(false);
      debouncedGetSuggestions(value);
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    console.log('🎯 SearchSection.handleSubmit 被调用！');
    console.log('🎯 事件对象:', e);
    console.log('🎯 当前query:', query);
    console.log('🎯 isSearching:', isSearching);
    console.log('🎯 onSearch函数:', typeof onSearch, onSearch);

    e.preventDefault();
    console.log(`🎯 SearchSection: 提交搜索表单, query="${query}", isSearching=${isSearching}`);

    if (query.trim()) {
      const trimmedQuery = query.trim();
      console.log(`📝 SearchSection: 保存搜索历史并调用onSearch`);
      console.log(`📞 即将调用 onSearch("${trimmedQuery}")`);

      saveSearchHistory(trimmedQuery);
      onSearch(trimmedQuery);

      console.log(`✅ onSearch 调用完成`);
      setShowSuggestions(false);
      setShowHistory(false);
    } else {
      console.log(`❌ SearchSection: 搜索关键词为空`);
    }
  };

  const handleSuggestionClick = (suggestion: SearchSuggestion) => {
    setQuery(suggestion.title);
    saveSearchHistory(suggestion.title);
    onSearch(suggestion.title);
    setShowSuggestions(false);
    setShowHistory(false);
  };

  const handleHistoryClick = (historyItem: string) => {
    setQuery(historyItem);
    onSearch(historyItem);
    setShowHistory(false);
  };

  const clearSearchHistory = () => {
    searchHistoryManager.clearHistory();
    setSearchHistory([]);
    setShowHistory(false);
  };

  const handleShowFullHistory = () => {
    setShowFullHistory(true);
    setShowSuggestions(false);
    setShowHistory(false);
  };

  const handleHistorySelect = (query: string) => {
    setQuery(query);
    onSearch(query);
    setShowFullHistory(false);
    setShowSuggestions(false);
    setShowHistory(false);
  };

  const handleInputFocus = () => {
    if (query.trim() === '' && searchHistory.length > 0) {
      setShowHistory(true);
    } else if (query.trim().length >= 2) {
      setShowSuggestions(true);
    }
  };

  // 点击外部关闭建议框
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        suggestionsRef.current &&
        !suggestionsRef.current.contains(event.target as Node) &&
        !searchInputRef.current?.contains(event.target as Node)
      ) {
        setShowSuggestions(false);
        setShowHistory(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // 清理防抖定时器
  useEffect(() => {
    return () => {
      if (debounceRef.current) {
        clearTimeout(debounceRef.current);
      }
    };
  }, []);

  return (
    <section className="relative py-20 px-4 sm:px-6 lg:px-8">
      <div className="max-w-4xl mx-auto text-center">
        {/* Hero Section */}
        <div className="mb-12">
          <div className="flex items-center justify-center mb-6">
            <div className="flex items-center justify-center w-16 h-16 bg-gradient-to-br from-primary-500 to-primary-600 rounded-2xl shadow-2xl">
              <Sparkles className="w-8 h-8 text-white" />
            </div>
          </div>
          <h1 className="text-4xl sm:text-5xl lg:text-6xl font-bold text-slate-900 mb-6">
            AI影视解说
            <span className="block bg-gradient-to-r from-primary-600 to-secondary-600 bg-clip-text text-transparent">
              创作新体验
            </span>
          </h1>
          <p className="text-xl text-slate-600 max-w-2xl mx-auto leading-relaxed">
            输入电影或电视剧名称，AI为您生成高质量解说文案，
            <br className="hidden sm:block" />
            让创作从"天"缩短至"分钟"
          </p>
        </div>

        {/* Error Message */}
        {error && (
          <div className="max-w-2xl mx-auto mb-6">
            <div className="flex items-center justify-between p-4 bg-error-50 border border-error-200 rounded-xl">
              <div className="flex items-center space-x-3">
                <AlertCircle className="w-5 h-5 text-error-500 flex-shrink-0" />
                <span className="text-error-700 text-sm">{error}</span>
              </div>
              {onClearError && (
                <button
                  onClick={onClearError}
                  className="text-error-400 hover:text-error-600 transition-colors"
                >
                  <X className="w-4 h-4" />
                </button>
              )}
            </div>
          </div>
        )}

        {/* Search Form */}
        <form onSubmit={handleSubmit} className="relative max-w-2xl mx-auto mb-12">
          <div className="relative">
            <Search className="absolute left-6 top-1/2 transform -translate-y-1/2 w-6 h-6 text-slate-400 z-10" />
            <input
              ref={searchInputRef}
              type="text"
              value={query}
              onChange={handleInputChange}
              onFocus={handleInputFocus}
              placeholder="搜索电影或电视剧名称..."
              className={`w-full pl-16 pr-32 py-5 text-lg border-2 rounded-2xl focus:ring-4 focus:ring-primary-100 outline-none transition-all bg-white shadow-lg ${
                error
                  ? 'border-error-300 focus:border-error-500'
                  : 'border-slate-200 focus:border-primary-500'
              } ${
                (showSuggestions || showHistory)
                  ? 'rounded-b-none border-b-0'
                  : ''
              }`}
              disabled={isSearching}
              autoComplete="off"
            />
            <button
              type="submit"
              disabled={isSearching || !query.trim()}
              className="absolute right-3 top-1/2 transform -translate-y-1/2 px-6 py-3 bg-gradient-to-r from-primary-500 to-primary-600 text-white rounded-xl hover:from-primary-600 hover:to-primary-700 disabled:opacity-50 disabled:cursor-not-allowed transition-all font-medium shadow-lg hover:shadow-xl z-10"
            >
              {isSearching ? (
                <div className="flex items-center space-x-2">
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                  <span>搜索中...</span>
                </div>
              ) : (
                '开始创作'
              )}
            </button>

            {/* Search Suggestions & History Dropdown */}
            {(showSuggestions || showHistory) && (
              <div
                ref={suggestionsRef}
                className="absolute top-full left-0 right-0 bg-white border-2 border-t-0 border-slate-200 rounded-b-2xl shadow-xl z-20 max-h-80 overflow-y-auto"
              >
                {/* Loading State */}
                {isLoadingSuggestions && (
                  <div className="p-4 text-center">
                    <div className="inline-flex items-center space-x-2 text-slate-500">
                      <div className="w-4 h-4 border-2 border-slate-300 border-t-primary-500 rounded-full animate-spin"></div>
                      <span className="text-sm">搜索建议加载中...</span>
                    </div>
                  </div>
                )}

                {/* Search Suggestions */}
                {showSuggestions && suggestions.length > 0 && !isLoadingSuggestions && (
                  <div className="py-2">
                    <div className="px-4 py-2 text-xs font-medium text-slate-500 uppercase tracking-wide border-b border-slate-100">
                      搜索建议
                    </div>
                    {suggestions.map((suggestion) => (
                      <button
                        key={suggestion.id}
                        onClick={() => handleSuggestionClick(suggestion)}
                        className="w-full px-4 py-3 text-left hover:bg-slate-50 transition-colors flex items-center justify-between group"
                      >
                        <div className="flex items-center space-x-3">
                          <Search className="w-4 h-4 text-slate-400" />
                          <div>
                            <div className="text-slate-900 group-hover:text-primary-600 transition-colors">
                              {suggestion.title}
                            </div>
                            <div className="text-xs text-slate-500">
                              {suggestion.type === 'movie' ? '电影' : '电视剧'}
                              {suggestion.year && ` • ${suggestion.year}`}
                            </div>
                          </div>
                        </div>
                      </button>
                    ))}
                  </div>
                )}

                {/* Search History */}
                {showHistory && searchHistory.length > 0 && (
                  <div className="py-2">
                    <div className="flex items-center justify-between px-4 py-2 border-b border-slate-100">
                      <div className="text-xs font-medium text-slate-500 uppercase tracking-wide">
                        搜索历史
                      </div>
                      <div className="flex items-center space-x-2">
                        <button
                          onClick={handleShowFullHistory}
                          className="text-xs text-primary-600 hover:text-primary-700 transition-colors"
                        >
                          查看全部
                        </button>
                        <button
                          onClick={clearSearchHistory}
                          className="text-xs text-slate-400 hover:text-slate-600 transition-colors"
                        >
                          清除
                        </button>
                      </div>
                    </div>
                    {searchHistory.slice(0, 5).map((historyItem, index) => (
                      <button
                        key={index}
                        onClick={() => handleHistoryClick(historyItem)}
                        className="w-full px-4 py-3 text-left hover:bg-slate-50 transition-colors flex items-center space-x-3 group"
                      >
                        <Clock className="w-4 h-4 text-slate-400" />
                        <span className="text-slate-700 group-hover:text-primary-600 transition-colors">
                          {historyItem}
                        </span>
                      </button>
                    ))}
                    {searchHistory.length > 5 && (
                      <button
                        onClick={handleShowFullHistory}
                        className="w-full px-4 py-2 text-center text-sm text-primary-600 hover:text-primary-700 hover:bg-primary-50 transition-colors border-t border-slate-100"
                      >
                        查看更多历史记录 ({searchHistory.length - 5} 条)
                      </button>
                    )}
                  </div>
                )}

                {/* Empty State */}
                {showSuggestions && suggestions.length === 0 && !isLoadingSuggestions && query.trim().length >= 2 && (
                  <div className="p-4 text-center text-slate-500 text-sm">
                    暂无搜索建议
                  </div>
                )}
              </div>
            )}
          </div>
        </form>

        {/* Quick Actions */}
        <div className="flex flex-wrap items-center justify-center gap-4 text-sm">
          <span className="text-slate-500 flex items-center space-x-1">
            <TrendingUp className="w-4 h-4" />
            <span>热门搜索:</span>
          </span>
          {['流浪地球2', '满江红', '深海', '阿凡达2', '狂飙', '三体'].map((term) => (
            <button
              key={term}
              onClick={() => {
                setQuery(term);
                saveSearchHistory(term);
                onSearch(term);
                setShowSuggestions(false);
                setShowHistory(false);
              }}
              disabled={isSearching}
              className="px-4 py-2 bg-slate-100 hover:bg-primary-50 hover:text-primary-600 text-slate-600 rounded-lg transition-all disabled:opacity-50 disabled:cursor-not-allowed hover:shadow-md"
            >
              {term}
            </button>
          ))}
        </div>

        {/* Search Tips */}
        {!isSearching && !error && (
          <div className="mt-8 text-center">
            <p className="text-sm text-slate-500">
              💡 小贴士：支持中英文搜索，可输入电影名、导演名或演员名
            </p>
            {searchHistory.length > 0 && (
              <button
                onClick={handleShowFullHistory}
                className="mt-2 inline-flex items-center space-x-1 text-sm text-primary-600 hover:text-primary-700 transition-colors"
              >
                <History className="w-4 h-4" />
                <span>查看搜索历史</span>
              </button>
            )}
          </div>
        )}
      </div>

      {/* Background Decoration */}
      <div className="absolute inset-0 -z-10 overflow-hidden">
        <div className="absolute top-1/4 left-1/4 w-64 h-64 bg-primary-200 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-pulse-slow"></div>
        <div className="absolute top-1/3 right-1/4 w-72 h-72 bg-secondary-200 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-pulse-slow" style={{ animationDelay: '1s' }}></div>
        <div className="absolute bottom-1/4 left-1/3 w-56 h-56 bg-accent-200 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-pulse-slow" style={{ animationDelay: '2s' }}></div>
      </div>

      {/* Full Search History Modal */}
      {showFullHistory && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
          <div className="w-full max-w-2xl max-h-[80vh] overflow-hidden">
            <SearchHistory
              onSearchSelect={handleHistorySelect}
              onClose={() => setShowFullHistory(false)}
              maxItems={50}
            />
          </div>
        </div>
      )}
    </section>
  );
};

export default SearchSection;