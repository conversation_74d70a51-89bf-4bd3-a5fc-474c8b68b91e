import React, { useState, useEffect } from 'react';
import { Bug, Clock, Wifi, Server, AlertTriangle, CheckCircle, XCircle } from 'lucide-react';

interface SearchDebugInfo {
  query: string;
  startTime: number;
  endTime?: number;
  status: 'idle' | 'searching' | 'success' | 'error';
  method?: 'supabase' | 'fetch' | 'fallback';
  error?: string;
  resultCount?: number;
  cached?: boolean;
  duration?: number;
}

interface SearchDebugPanelProps {
  isVisible?: boolean;
  onToggle?: () => void;
}

const SearchDebugPanel: React.FC<SearchDebugPanelProps> = ({ 
  isVisible = false, 
  onToggle 
}) => {
  const [debugInfo, setDebugInfo] = useState<SearchDebugInfo[]>([]);
  const [networkStatus, setNetworkStatus] = useState<'online' | 'offline'>('online');

  // 监听网络状态
  useEffect(() => {
    const updateNetworkStatus = () => {
      setNetworkStatus(navigator.onLine ? 'online' : 'offline');
    };

    window.addEventListener('online', updateNetworkStatus);
    window.addEventListener('offline', updateNetworkStatus);

    return () => {
      window.removeEventListener('online', updateNetworkStatus);
      window.removeEventListener('offline', updateNetworkStatus);
    };
  }, []);

  // 监听搜索事件（通过自定义事件）
  useEffect(() => {
    const handleSearchStart = (event: CustomEvent) => {
      const { query } = event.detail;
      const newDebugInfo: SearchDebugInfo = {
        query,
        startTime: Date.now(),
        status: 'searching'
      };
      
      setDebugInfo(prev => [newDebugInfo, ...prev.slice(0, 9)]); // 保留最近10条记录
    };

    const handleSearchEnd = (event: CustomEvent) => {
      const { query, success, error, resultCount, method, cached } = event.detail;
      const endTime = Date.now();
      
      setDebugInfo(prev => {
        const updated = [...prev];
        const index = updated.findIndex(info => 
          info.query === query && info.status === 'searching'
        );
        
        if (index !== -1) {
          updated[index] = {
            ...updated[index],
            endTime,
            status: success ? 'success' : 'error',
            error,
            resultCount,
            method,
            cached,
            duration: endTime - updated[index].startTime
          };
        }
        
        return updated;
      });
    };

    window.addEventListener('search-start', handleSearchStart as EventListener);
    window.addEventListener('search-end', handleSearchEnd as EventListener);

    return () => {
      window.removeEventListener('search-start', handleSearchStart as EventListener);
      window.removeEventListener('search-end', handleSearchEnd as EventListener);
    };
  }, []);

  const getStatusIcon = (status: SearchDebugInfo['status']) => {
    switch (status) {
      case 'searching':
        return <Clock className="w-4 h-4 text-blue-500 animate-spin" />;
      case 'success':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'error':
        return <XCircle className="w-4 h-4 text-red-500" />;
      default:
        return <Clock className="w-4 h-4 text-gray-400" />;
    }
  };

  const getMethodColor = (method?: string) => {
    switch (method) {
      case 'supabase':
        return 'bg-green-100 text-green-800';
      case 'fetch':
        return 'bg-blue-100 text-blue-800';
      case 'fallback':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const formatDuration = (duration?: number) => {
    if (!duration) return '-';
    if (duration < 1000) return `${duration}ms`;
    return `${(duration / 1000).toFixed(1)}s`;
  };

  const clearDebugInfo = () => {
    setDebugInfo([]);
  };

  const testConnection = async () => {
    try {
      // 发送测试搜索事件
      window.dispatchEvent(new CustomEvent('search-start', {
        detail: { query: 'test-connection' }
      }));

      const response = await fetch('/api/health', { 
        method: 'GET',
        signal: AbortSignal.timeout(5000)
      });
      
      window.dispatchEvent(new CustomEvent('search-end', {
        detail: { 
          query: 'test-connection',
          success: response.ok,
          method: 'fetch',
          resultCount: response.ok ? 1 : 0
        }
      }));
    } catch (error) {
      window.dispatchEvent(new CustomEvent('search-end', {
        detail: { 
          query: 'test-connection',
          success: false,
          error: error instanceof Error ? error.message : 'Connection test failed',
          method: 'fetch'
        }
      }));
    }
  };

  if (!isVisible) {
    return (
      <button
        onClick={onToggle}
        className="fixed bottom-4 right-4 p-3 bg-gray-800 text-white rounded-full shadow-lg hover:bg-gray-700 transition-colors z-50"
        title="显示调试面板"
      >
        <Bug className="w-5 h-5" />
      </button>
    );
  }

  return (
    <div className="fixed bottom-4 right-4 w-96 max-h-96 bg-white border border-gray-200 rounded-lg shadow-xl z-50 overflow-hidden">
      {/* Header */}
      <div className="p-3 bg-gray-50 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Bug className="w-4 h-4 text-gray-600" />
            <span className="text-sm font-medium text-gray-900">搜索调试</span>
            <div className="flex items-center space-x-1">
              <Wifi className={`w-3 h-3 ${networkStatus === 'online' ? 'text-green-500' : 'text-red-500'}`} />
              <span className={`text-xs ${networkStatus === 'online' ? 'text-green-600' : 'text-red-600'}`}>
                {networkStatus === 'online' ? '在线' : '离线'}
              </span>
            </div>
          </div>
          
          <div className="flex items-center space-x-1">
            <button
              onClick={testConnection}
              className="p-1 text-gray-400 hover:text-gray-600 transition-colors"
              title="测试连接"
            >
              <Server className="w-4 h-4" />
            </button>
            <button
              onClick={clearDebugInfo}
              className="p-1 text-gray-400 hover:text-gray-600 transition-colors"
              title="清除记录"
            >
              <AlertTriangle className="w-4 h-4" />
            </button>
            <button
              onClick={onToggle}
              className="p-1 text-gray-400 hover:text-gray-600 transition-colors"
              title="关闭"
            >
              ×
            </button>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="max-h-80 overflow-y-auto">
        {debugInfo.length === 0 ? (
          <div className="p-4 text-center text-gray-500 text-sm">
            暂无搜索记录
          </div>
        ) : (
          <div className="divide-y divide-gray-100">
            {debugInfo.map((info, index) => (
              <div key={index} className="p-3">
                <div className="flex items-start justify-between mb-2">
                  <div className="flex items-center space-x-2 min-w-0 flex-1">
                    {getStatusIcon(info.status)}
                    <span className="text-sm font-medium text-gray-900 truncate">
                      {info.query}
                    </span>
                  </div>
                  <span className="text-xs text-gray-500 ml-2">
                    {formatDuration(info.duration)}
                  </span>
                </div>
                
                <div className="flex items-center justify-between text-xs">
                  <div className="flex items-center space-x-2">
                    {info.method && (
                      <span className={`px-2 py-1 rounded-full ${getMethodColor(info.method)}`}>
                        {info.method}
                      </span>
                    )}
                    {info.cached && (
                      <span className="px-2 py-1 bg-purple-100 text-purple-800 rounded-full">
                        缓存
                      </span>
                    )}
                  </div>
                  
                  {info.status === 'success' && info.resultCount !== undefined && (
                    <span className="text-green-600">
                      {info.resultCount} 个结果
                    </span>
                  )}
                </div>
                
                {info.error && (
                  <div className="mt-2 p-2 bg-red-50 border border-red-200 rounded text-xs text-red-700">
                    {info.error}
                  </div>
                )}
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default SearchDebugPanel;

// 辅助函数：发送搜索开始事件
export const emitSearchStart = (query: string) => {
  window.dispatchEvent(new CustomEvent('search-start', {
    detail: { query }
  }));
};

// 辅助函数：发送搜索结束事件
export const emitSearchEnd = (details: {
  query: string;
  success: boolean;
  error?: string;
  resultCount?: number;
  method?: string;
  cached?: boolean;
}) => {
  window.dispatchEvent(new CustomEvent('search-end', {
    detail: details
  }));
};
