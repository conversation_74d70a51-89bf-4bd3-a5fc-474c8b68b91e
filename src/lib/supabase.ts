import { createClient } from '@supabase/supabase-js';

const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY;

console.log('🔧 Supabase配置:', {
  url: supabaseUrl ? `${supabaseUrl.substring(0, 30)}...` : '未设置',
  key: supabaseAnonKey ? `${supabaseAnonKey.substring(0, 30)}...` : '未设置'
});

if (!supabaseUrl || !supabaseAnonKey) {
  console.error('❌ Supabase环境变量缺失:', { supabaseUrl, supabaseAnonKey });
  throw new Error('Missing Supabase environment variables');
}

// 创建Supabase客户端，配置会话持久化和自动刷新
export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    // 启用会话持久化，将会话存储在localStorage中
    persistSession: true,
    // 启用自动刷新token，防止会话过期
    autoRefreshToken: true,
    // 检测URL中的会话信息（用于邮箱确认等场景）
    detectSessionInUrl: true,
    // 移除自定义storageKey，使用默认值
    // storageKey: 'narratoai-auth-token',
  },
});

console.log('✅ Supabase客户端已初始化，启用会话持久化和自动刷新');

// Types
export interface UserProfile {
  id: string;
  email: string;
  full_name?: string;
  avatar_url?: string;
  subscription_tier: 'free' | 'pro' | 'premium';
  subscription_status: 'inactive' | 'active' | 'canceled' | 'past_due';
  stripe_customer_id?: string;
  subscription_end_date?: string;
  usage_count: number;
  monthly_limit: number;
  created_at: string;
  updated_at: string;
}

export interface UserProject {
  id: string;
  user_id: string;
  media_id: string;
  title: string;
  script_content?: string;
  status: 'draft' | 'processing' | 'completed' | 'exported';
  created_at: string;
  updated_at: string;
}