/**
 * TMDB 类型映射
 * 将TMDB API返回的genre_ids转换为中文类型名称
 */

// 电影类型映射
export const MOVIE_GENRES: Record<number, string> = {
  28: '动作',
  12: '冒险',
  16: '动画',
  35: '喜剧',
  80: '犯罪',
  99: '纪录片',
  18: '剧情',
  10751: '家庭',
  14: '奇幻',
  36: '历史',
  27: '恐怖',
  10402: '音乐',
  9648: '悬疑',
  10749: '爱情',
  878: '科幻',
  10770: '电视电影',
  53: '惊悚',
  10752: '战争',
  37: '西部'
};

// 电视剧类型映射
export const TV_GENRES: Record<number, string> = {
  10759: '动作冒险',
  16: '动画',
  35: '喜剧',
  80: '犯罪',
  99: '纪录片',
  18: '剧情',
  10751: '家庭',
  10762: '儿童',
  9648: '悬疑',
  10763: '新闻',
  10764: '真人秀',
  10765: '科幻奇幻',
  10766: '肥皂剧',
  10767: '脱口秀',
  10768: '战争政治',
  37: '西部'
};

// 合并的类型映射（用于多类型搜索）
export const ALL_GENRES: Record<number, string> = {
  ...MOVIE_GENRES,
  ...TV_GENRES
};

/**
 * 将genre_ids转换为中文类型名称数组
 */
export function mapGenreIds(genreIds: number[], mediaType?: string): string[] {
  const genreMap = mediaType === 'tv' ? TV_GENRES : 
                   mediaType === 'movie' ? MOVIE_GENRES : 
                   ALL_GENRES;
  
  return genreIds
    .map(id => genreMap[id])
    .filter(Boolean) // 过滤掉未知的类型ID
    .slice(0, 5); // 最多显示5个类型
}

/**
 * 获取所有可用的类型选项（用于过滤器）
 */
export function getAvailableGenres(mediaType?: string): Array<{ id: number; name: string }> {
  const genreMap = mediaType === 'tv' ? TV_GENRES : 
                   mediaType === 'movie' ? MOVIE_GENRES : 
                   ALL_GENRES;
  
  return Object.entries(genreMap)
    .map(([id, name]) => ({ id: parseInt(id), name }))
    .sort((a, b) => a.name.localeCompare(b.name));
}

/**
 * 根据类型名称获取类型ID
 */
export function getGenreIdByName(genreName: string, mediaType?: string): number | null {
  const genreMap = mediaType === 'tv' ? TV_GENRES : 
                   mediaType === 'movie' ? MOVIE_GENRES : 
                   ALL_GENRES;
  
  const entry = Object.entries(genreMap).find(([_, name]) => name === genreName);
  return entry ? parseInt(entry[0]) : null;
}

/**
 * 检查电影/电视剧是否包含指定类型
 */
export function hasGenre(genreIds: number[], targetGenreId: number): boolean {
  return genreIds.includes(targetGenreId);
}

/**
 * 获取热门类型（基于常见程度排序）
 */
export function getPopularGenres(mediaType?: string): Array<{ id: number; name: string }> {
  // 定义热门类型的优先级
  const popularMovieGenres = [18, 35, 28, 878, 53, 10749, 27, 12, 16, 80];
  const popularTvGenres = [18, 35, 80, 9648, 10759, 16, 10765, 10764, 99, 10751];
  
  const popularIds = mediaType === 'tv' ? popularTvGenres : 
                     mediaType === 'movie' ? popularMovieGenres : 
                     [...new Set([...popularMovieGenres, ...popularTvGenres])];
  
  const genreMap = mediaType === 'tv' ? TV_GENRES : 
                   mediaType === 'movie' ? MOVIE_GENRES : 
                   ALL_GENRES;
  
  return popularIds
    .map(id => ({ id, name: genreMap[id] }))
    .filter(genre => genre.name) // 过滤掉未定义的类型
    .slice(0, 10); // 返回前10个热门类型
}

/**
 * 根据年份范围过滤
 */
export interface YearRange {
  start?: number;
  end?: number;
}

export function filterByYearRange(releaseDate: string | null, yearRange: YearRange): boolean {
  if (!releaseDate) return true; // 如果没有发布日期，不过滤
  
  const year = new Date(releaseDate).getFullYear();
  
  if (yearRange.start && year < yearRange.start) return false;
  if (yearRange.end && year > yearRange.end) return false;
  
  return true;
}

/**
 * 根据评分范围过滤
 */
export interface RatingRange {
  min?: number;
  max?: number;
}

export function filterByRatingRange(voteAverage: number, ratingRange: RatingRange): boolean {
  if (ratingRange.min && voteAverage < ratingRange.min) return false;
  if (ratingRange.max && voteAverage > ratingRange.max) return false;
  
  return true;
}

/**
 * 预定义的年份范围选项
 */
export const YEAR_RANGE_OPTIONS = [
  { label: '全部年份', value: null },
  { label: '2020年代', value: { start: 2020, end: 2029 } },
  { label: '2010年代', value: { start: 2010, end: 2019 } },
  { label: '2000年代', value: { start: 2000, end: 2009 } },
  { label: '1990年代', value: { start: 1990, end: 1999 } },
  { label: '1980年代', value: { start: 1980, end: 1989 } },
  { label: '更早', value: { start: 1900, end: 1979 } }
];

/**
 * 预定义的评分范围选项
 */
export const RATING_RANGE_OPTIONS = [
  { label: '全部评分', value: null },
  { label: '9.0+', value: { min: 9.0, max: 10.0 } },
  { label: '8.0+', value: { min: 8.0, max: 10.0 } },
  { label: '7.0+', value: { min: 7.0, max: 10.0 } },
  { label: '6.0+', value: { min: 6.0, max: 10.0 } },
  { label: '5.0+', value: { min: 5.0, max: 10.0 } },
  { label: '5.0以下', value: { min: 0.0, max: 5.0 } }
];
