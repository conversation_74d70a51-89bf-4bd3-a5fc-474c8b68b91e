/**
 * 搜索历史管理工具
 * 提供搜索历史的存储、检索、管理功能
 */

export interface SearchHistoryItem {
  id: string;
  query: string;
  timestamp: number;
  resultCount?: number;
  mediaType?: 'movie' | 'tv' | 'multi';
}

export interface SearchHistoryStats {
  totalSearches: number;
  uniqueQueries: number;
  mostSearchedQuery: string;
  recentSearches: SearchHistoryItem[];
}

class SearchHistoryManager {
  private readonly STORAGE_KEY = 'narrato-search-history';
  private readonly MAX_HISTORY_SIZE = 50;
  private history: SearchHistoryItem[] = [];

  constructor() {
    this.loadHistory();
  }

  /**
   * 从localStorage加载搜索历史
   */
  private loadHistory(): void {
    try {
      const stored = localStorage.getItem(this.STORAGE_KEY);
      if (stored) {
        this.history = JSON.parse(stored);
        // 验证数据格式并清理无效数据
        this.history = this.history.filter(item => 
          item && 
          typeof item.query === 'string' && 
          typeof item.timestamp === 'number' &&
          item.query.trim().length > 0
        );
        // 按时间戳降序排序
        this.history.sort((a, b) => b.timestamp - a.timestamp);
      }
    } catch (error) {
      console.error('Failed to load search history:', error);
      this.history = [];
    }
  }

  /**
   * 保存搜索历史到localStorage
   */
  private saveHistory(): void {
    try {
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(this.history));
    } catch (error) {
      console.error('Failed to save search history:', error);
    }
  }

  /**
   * 添加搜索记录
   */
  addSearch(query: string, resultCount?: number, mediaType?: 'movie' | 'tv' | 'multi'): void {
    const trimmedQuery = query.trim();
    if (!trimmedQuery) return;

    // 移除已存在的相同查询
    this.history = this.history.filter(item => 
      item.query.toLowerCase() !== trimmedQuery.toLowerCase()
    );

    // 添加新记录到开头
    const newItem: SearchHistoryItem = {
      id: `search-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      query: trimmedQuery,
      timestamp: Date.now(),
      resultCount,
      mediaType
    };

    this.history.unshift(newItem);

    // 限制历史记录数量
    if (this.history.length > this.MAX_HISTORY_SIZE) {
      this.history = this.history.slice(0, this.MAX_HISTORY_SIZE);
    }

    this.saveHistory();
  }

  /**
   * 获取搜索历史
   */
  getHistory(limit?: number): SearchHistoryItem[] {
    const result = limit ? this.history.slice(0, limit) : this.history;
    return [...result]; // 返回副本
  }

  /**
   * 获取最近的搜索查询（仅查询字符串）
   */
  getRecentQueries(limit: number = 10): string[] {
    return this.history.slice(0, limit).map(item => item.query);
  }

  /**
   * 搜索历史记录
   */
  searchHistory(searchTerm: string, limit: number = 10): SearchHistoryItem[] {
    const term = searchTerm.toLowerCase().trim();
    if (!term) return this.getHistory(limit);

    return this.history
      .filter(item => item.query.toLowerCase().includes(term))
      .slice(0, limit);
  }

  /**
   * 删除特定搜索记录
   */
  removeSearch(id: string): boolean {
    const initialLength = this.history.length;
    this.history = this.history.filter(item => item.id !== id);
    
    if (this.history.length < initialLength) {
      this.saveHistory();
      return true;
    }
    return false;
  }

  /**
   * 删除特定查询的所有记录
   */
  removeQuery(query: string): number {
    const initialLength = this.history.length;
    this.history = this.history.filter(item => 
      item.query.toLowerCase() !== query.toLowerCase()
    );
    
    const removedCount = initialLength - this.history.length;
    if (removedCount > 0) {
      this.saveHistory();
    }
    return removedCount;
  }

  /**
   * 清空所有搜索历史
   */
  clearHistory(): void {
    this.history = [];
    this.saveHistory();
  }

  /**
   * 获取搜索统计信息
   */
  getStats(): SearchHistoryStats {
    const queryCount = new Map<string, number>();
    
    this.history.forEach(item => {
      const query = item.query.toLowerCase();
      queryCount.set(query, (queryCount.get(query) || 0) + 1);
    });

    let mostSearchedQuery = '';
    let maxCount = 0;
    
    queryCount.forEach((count, query) => {
      if (count > maxCount) {
        maxCount = count;
        mostSearchedQuery = query;
      }
    });

    return {
      totalSearches: this.history.length,
      uniqueQueries: queryCount.size,
      mostSearchedQuery,
      recentSearches: this.getHistory(5)
    };
  }

  /**
   * 导出搜索历史
   */
  exportHistory(): string {
    return JSON.stringify({
      exportDate: new Date().toISOString(),
      version: '1.0',
      history: this.history
    }, null, 2);
  }

  /**
   * 导入搜索历史
   */
  importHistory(jsonData: string): boolean {
    try {
      const data = JSON.parse(jsonData);
      if (data.history && Array.isArray(data.history)) {
        // 验证导入的数据格式
        const validHistory = data.history.filter((item: any) => 
          item && 
          typeof item.query === 'string' && 
          typeof item.timestamp === 'number' &&
          item.query.trim().length > 0
        );

        // 合并现有历史和导入的历史，去重
        const mergedHistory = [...this.history];
        
        validHistory.forEach((importedItem: SearchHistoryItem) => {
          const exists = mergedHistory.some(existing => 
            existing.query.toLowerCase() === importedItem.query.toLowerCase() &&
            Math.abs(existing.timestamp - importedItem.timestamp) < 1000 // 1秒内的认为是重复
          );
          
          if (!exists) {
            mergedHistory.push(importedItem);
          }
        });

        // 排序并限制数量
        mergedHistory.sort((a, b) => b.timestamp - a.timestamp);
        this.history = mergedHistory.slice(0, this.MAX_HISTORY_SIZE);
        
        this.saveHistory();
        return true;
      }
    } catch (error) {
      console.error('Failed to import search history:', error);
    }
    return false;
  }

  /**
   * 获取历史记录大小限制
   */
  getMaxHistorySize(): number {
    return this.MAX_HISTORY_SIZE;
  }

  /**
   * 检查是否有搜索历史
   */
  hasHistory(): boolean {
    return this.history.length > 0;
  }
}

// 创建全局实例
export const searchHistoryManager = new SearchHistoryManager();

// 导出便捷函数
export const addSearchToHistory = (query: string, resultCount?: number, mediaType?: 'movie' | 'tv' | 'multi') => {
  searchHistoryManager.addSearch(query, resultCount, mediaType);
};

export const getSearchHistory = (limit?: number) => {
  return searchHistoryManager.getHistory(limit);
};

export const clearSearchHistory = () => {
  searchHistoryManager.clearHistory();
};

export const searchInHistory = (searchTerm: string, limit?: number) => {
  return searchHistoryManager.searchHistory(searchTerm, limit);
};
