import React, { createContext, useContext, useEffect, useState } from 'react';
import { User, Session } from '@supabase/supabase-js';
import { supabase, UserProfile } from '../lib/supabase';
import { sessionDebugger } from '../utils/sessionDebugger';

interface AuthContextType {
  user: User | null;
  profile: UserProfile | null;
  session: Session | null;
  loading: boolean;
  signUp: (email: string, password: string, fullName: string) => Promise<{ error: any }>;
  signIn: (email: string, password: string) => Promise<{ error: any }>;
  signOut: () => Promise<void>;
  updateProfile: (updates: Partial<UserProfile>) => Promise<{ error: any }>;
  canCreateProject: () => boolean;
  incrementUsage: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [profile, setProfile] = useState<UserProfile | null>(null);
  const [session, setSession] = useState<Session | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    console.log('🔄 AuthContext初始化中...');

    // 初始化会话恢复逻辑
    const initializeAuth = async () => {
      try {
        // 获取初始会话，这会从localStorage中恢复会话
        const { data: { session }, error } = await supabase.auth.getSession();

        console.log('📱 获取初始会话:', {
          hasSession: !!session,
          hasUser: !!session?.user,
          sessionExpiry: session?.expires_at ? new Date(session.expires_at * 1000).toISOString() : null,
          error: error?.message
        });

        if (error) {
          console.error('❌ 获取会话时出错:', error);
          setLoading(false);
          return;
        }

        // 设置会话和用户状态
        setSession(session);
        setUser(session?.user ?? null);

        if (session?.user) {
          console.log('👤 用户已登录，获取用户档案...');
          await fetchProfile(session.user.id);
        } else {
          console.log('👤 用户未登录');
          setLoading(false);
        }
      } catch (error) {
        console.error('❌ 初始化认证时出错:', error);
        setLoading(false);
      }
    };

    // 监听认证状态变化
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        console.log('🔄 认证状态变化:', {
          event,
          hasSession: !!session,
          hasUser: !!session?.user,
          userEmail: session?.user?.email
        });

        setSession(session);
        setUser(session?.user ?? null);

        if (session?.user) {
          await fetchProfile(session.user.id);
        } else {
          setProfile(null);
          setLoading(false);
        }
      }
    );

    // 执行初始化
    initializeAuth();

    // 在开发环境下进行会话状态检查
    if (import.meta.env.DEV) {
      setTimeout(() => {
        sessionDebugger.checkSessionStatus();
      }, 1000);
    }

    return () => {
      console.log('🔄 清理认证监听器');
      subscription.unsubscribe();
    };
  }, []);

  const fetchProfile = async (userId: string) => {
    try {
      console.log('📋 开始获取用户档案:', { userId });

      const { data, error } = await supabase
        .from('user_profiles')
        .select('*')
        .eq('id', userId)
        .single();

      if (error) {
        // If profile doesn't exist, it might be a new user
        if (error.code === 'PGRST116') {
          console.log('⏳ 用户档案未找到，可能是新注册用户，等待触发器处理...');
          // Wait a bit and try again in case the trigger is still processing
          setTimeout(() => fetchProfile(userId), 1000);
          return;
        }
        console.error('❌ 获取用户档案时出错:', error);
      } else {
        console.log('✅ 成功获取用户档案:', {
          email: data.email,
          fullName: data.full_name,
          subscriptionTier: data.subscription_tier,
          usageCount: data.usage_count
        });
        setProfile(data);
      }
    } catch (error) {
      console.error('❌ 获取用户档案时发生异常:', error);
    } finally {
      setLoading(false);
    }
  };

  const signUp = async (email: string, password: string, fullName: string) => {
    try {
      const { data, error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          emailRedirectTo: `${window.location.origin}/auth/confirm`,
          data: {
            full_name: fullName,
          },
        },
      });

      if (error) {
        return { error };
      }

      // If user is immediately confirmed (no email confirmation required)
      if (data.user && data.session) {
        // The trigger should create the profile, but let's wait a bit
        setTimeout(() => {
          if (data.user) {
            fetchProfile(data.user.id);
          }
        }, 500);
      }

      return { error: null };
    } catch (error) {
      console.error('Signup error:', error);
      return { error };
    }
  };

  const signIn = async (email: string, password: string) => {
    const { error } = await supabase.auth.signInWithPassword({
      email,
      password,
    });
    return { error };
  };

  const signOut = async () => {
    await supabase.auth.signOut();
  };

  const updateProfile = async (updates: Partial<UserProfile>) => {
    if (!user) return { error: new Error('No user logged in') };

    const { error } = await supabase
      .from('user_profiles')
      .update(updates)
      .eq('id', user.id);

    if (!error && profile) {
      setProfile({ ...profile, ...updates });
    }

    return { error };
  };

  const canCreateProject = () => {
    if (!profile) return false;
    if (profile.subscription_tier !== 'free') return true;
    return profile.usage_count < profile.monthly_limit;
  };

  const incrementUsage = async () => {
    if (!user || !profile) return;

    const { error } = await supabase
      .from('user_profiles')
      .update({ usage_count: profile.usage_count + 1 })
      .eq('id', user.id);

    if (!error) {
      setProfile({ ...profile, usage_count: profile.usage_count + 1 });
    }
  };

  const value = {
    user,
    profile,
    session,
    loading,
    signUp,
    signIn,
    signOut,
    updateProfile,
    canCreateProject,
    incrementUsage,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};