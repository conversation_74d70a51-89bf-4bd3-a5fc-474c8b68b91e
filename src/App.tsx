import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { AuthProvider } from './contexts/AuthContext';
import HomePage from './pages/HomePage';
import DetailsPage from './pages/DetailsPage';
import AuthConfirmPage from './pages/AuthConfirmPage';
import SearchTestPage from './pages/SearchTestPage';
import Header from './components/Header';

function App() {
  return (
    <AuthProvider>
      <Router>
        <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100">
          <Header />
          <main className="pt-20">
            <Routes>
              <Route path="/" element={<HomePage />} />
              <Route path="/details/:mediaId" element={<DetailsPage />} />
              <Route path="/auth/confirm" element={<AuthConfirmPage />} />
              <Route path="/test-search" element={<SearchTestPage />} />
            </Routes>
          </main>
        </div>
      </Router>
    </AuthProvider>
  );
}

export default App;