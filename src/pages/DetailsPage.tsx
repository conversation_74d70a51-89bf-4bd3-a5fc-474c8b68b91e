import React, { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import MovieDetails from '../components/MovieDetails';
import ScriptEditor from '../components/ScriptEditor';
import VideoUpload from '../components/VideoUpload';
import CopyrightAnalysis from '../components/CopyrightAnalysis';
import ExportSection from '../components/ExportSection';
import { getMovieDetails } from '../lib/movieApi';

const DetailsPage: React.FC = () => {
  const { mediaId } = useParams<{ mediaId: string }>();
  const [movieData, setMovieData] = useState<any>(null);
  const [scriptData, setScriptData] = useState<any>(null);
  const [uploadedVideo, setUploadedVideo] = useState<string | null>(null);
  const [copyrightAnalysis, setCopyrightAnalysis] = useState<any>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (mediaId) {
      fetchMovieDetails(mediaId);
    }
  }, [mediaId]);

  const fetchMovieDetails = async (id: string) => {
    setLoading(true);

    try {
      console.log('🎬 获取电影详情:', id);

      // 解析媒体ID和类型
      // 假设URL格式为: /details/movie-123 或 /details/tv-456
      const [type, movieId] = id.includes('-') ? id.split('-') : ['movie', id];
      const mediaType = (type === 'tv' ? 'tv' : 'movie') as 'movie' | 'tv';
      const numericId = parseInt(movieId, 10);

      if (isNaN(numericId)) {
        throw new Error('无效的媒体ID');
      }

      console.log('📡 调用movie-details API:', { id: numericId, type: mediaType });

      // 调用真实的API
      const response = await getMovieDetails(numericId, mediaType);

      console.log('✅ 获取电影详情成功:', response.data);

      // 转换API响应数据为组件期望的格式
      const movieData = {
        media_id: response.data.id.toString(),
        title: response.data.title,
        original_title: response.data.original_title,
        year: response.data.release_date ? new Date(response.data.release_date).getFullYear() : null,
        poster_url: response.data.poster_url,
        genres: response.data.genres?.map(g => g.name) || [],
        rating: response.data.vote_average,
        duration: response.data.runtime ? `${response.data.runtime} 分钟` : '未知',
        director: response.data.crew?.directors?.[0]?.name || '未知',
        actors: response.data.cast?.slice(0, 5).map(actor => actor.name) || [],
        plot_summary: response.data.overview || '暂无简介',
        backdrop_url: response.data.backdrop_url
      };

      setMovieData(movieData);
      setLoading(false);
    } catch (error) {
      console.error('❌ 获取电影详情失败:', error);
      setLoading(false);

      // 如果API调用失败，使用模拟数据作为降级方案
      console.log('🔄 使用模拟数据作为降级方案');
      const mockMovieData = {
        media_id: id,
        title: '流浪地球2',
        original_title: 'The Wandering Earth II',
        year: 2023,
        poster_url: 'https://images.unsplash.com/photo-1478720568477-b0ac8e6c4a17',
        genres: ['科幻', '冒险', '灾难'],
        rating: 8.3,
        duration: '173 分钟',
        director: '郭帆',
        actors: ['吴京', '刘德华', '李雪健', '沙溢', '宁理'],
        plot_summary: '面对即将结束的太阳，联合政府开启了"流浪地球"计划，建造了数万座行星发动机，带着地球一起逃离太阳系。在这个宏大计划实施的过程中，人类历史上最大的生存危机即将到来。',
        backdrop_url: 'https://images.unsplash.com/photo-1446776877081-d282a0f896e2'
      };

      setMovieData(mockMovieData);
    }
  };

  const handleScriptGenerated = (script: any) => {
    setScriptData(script);
  };

  const handleVideoUploaded = (videoUrl: string) => {
    setUploadedVideo(videoUrl);
    
    // Simulate copyright analysis
    setTimeout(() => {
      setCopyrightAnalysis({
        risk_level: 'medium',
        suggestions: [
          '检测到超过30秒的连续原始片段，建议增加画中画处理',
          '建议添加解说声音覆盖以降低版权风险',
          '可考虑对关键镜头进行镜像或调色处理'
        ]
      });
    }, 3000);
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="w-16 h-16 border-4 border-primary-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-slate-600">加载影片详情中...</p>
        </div>
      </div>
    );
  }

  if (!movieData) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <p className="text-slate-600">影片未找到</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen py-8 px-4 sm:px-6 lg:px-8">
      <div className="max-w-7xl mx-auto">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Left Column - Movie Details */}
          <div className="lg:col-span-1">
            <MovieDetails 
              movie={movieData} 
              onScriptGenerated={handleScriptGenerated}
            />
          </div>

          {/* Right Column - Script Editor and Tools */}
          <div className="lg:col-span-2 space-y-8">
            <ScriptEditor 
              scriptData={scriptData}
              movieTitle={movieData.title}
            />
            
            {scriptData && (
              <>
                <VideoUpload 
                  onVideoUploaded={handleVideoUploaded}
                  scriptId={scriptData.narration_id}
                />
                
                {copyrightAnalysis && (
                  <CopyrightAnalysis analysis={copyrightAnalysis} />
                )}
                
                {uploadedVideo && (
                  <ExportSection 
                    scriptId={scriptData.narration_id}
                    movieTitle={movieData.title}
                  />
                )}
              </>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default DetailsPage;