# NarratoAI 产品需求文档 (PRD)

**版本**: 1.0  
**日期**: 2024-07-30  
**状态**: 草稿  

## 1. 文档概述

### 1.1 产品愿景
NarratoAI 旨在成为全球领先的 AI 影视解说内容创作平台。我们的核心价值是：**比人类创作者更快、更深、更个性化，同时保证内容绝对安全合法**，最终解放内容创作者的生产力，并为广大影迷提供前所未有的观影辅助体验。

### 1.2 解决的核心问题
* **信息过载与时间稀缺**：普通观众没时间看完全部影视剧，但有强烈的社交需求和 FOMO（错失恐惧症）。
* **认知深度不足**：影迷希望深入理解电影，但信息搜集与分析成本高。
* **创作效率低下**：影视内容创作者在文案、素材、配音、剪辑上耗费大量时间，且面临灵感枯竭。
* **版权风险**：所有内容创作者都面临的法律风险。

### 1.3 目标用户画像
1. **高效追剧党 (核心用户 - 小明)**
   * **人物特征**：25-40岁，工作繁忙的白领或学生。社交圈经常讨论热门影视剧，有FOMO（错失恐惧症）。
   * **痛点**：
     * 没时间看2小时的电影，但想快速了解剧情，跟上话题。
     * 看了一部晦涩难懂的电影，想找个"课代表"给讲明白。
     * 想看某部电影，但不确定值不值得花时间，需要一个"无剧透推荐"或"快速剧情概览"。
   * **需求**：**快、准、核心**。要的是"干货"，快速了解剧情脉络、核心看点和主要人物关系。

2. **深度影迷/学生 (潜力用户 - 小红)**
   * **人物特征**：影视专业学生、资深影迷、影评人。
   * **痛点**：
     * 想深入研究电影的叙事结构、视听语言、象征意义，但信息搜集和整理过程繁琐。
     * 看完电影有很多感悟，想找个"AI对手"进行思想碰撞和探讨。
   * **需求**：**深、广、专业**。需要的不只是剧情，更是结构分析、主题探讨、彩蛋挖掘。

3. **内容创作者 (高价值用户 - 阿B)**
   * **人物特征**：B站、抖音、YouTube上的影视区UP主。
   * **痛点**：
     * 写解说文案、找素材、配音、剪辑，整个流程耗时耗力。
     * 选题和创作角度容易枯竭，需要灵感。
   * **需求**：**效率、工具、灵感**。需要的是一个能极大提升创作效率的生产力工具。

### 1.4 产品定位与竞品分析
NarratoAI 定位为面向大众的影视解说AI助手与内容创作平台，区别于：
* **传统影评网站**（如豆瓣、IMDb）：我们不只提供静态信息和用户评价，而是生成个性化、多媒体的解说内容。
* **短视频平台影视号**：相比人工创作的"X分钟看电影"，我们提供即时生成、更客观、可定制的解说体验。
* **通用AI助手**：相比ChatGPT等通用AI，我们的内容更准确，功能更专业化，体验更流畅。

## 2. 当前项目状态分析

### 2.1 技术栈概览
* **前端**:
  * **核心框架**: React 18 + TypeScript 5.x
  * **构建工具**: Vite 5.x
  * **UI/样式**: TailwindCSS 3.x + Lucide Icons
  * **路由**: React Router v6
* **后端**:
  * **核心服务**: Supabase
    * **认证**: Supabase Auth (基于JWT的认证系统)
    * **数据库**: PostgreSQL（托管于Supabase）
    * **Edge Functions**: 计划用于AI处理（基于Deno）
  * **计划中的AI服务**: Gemini / gemini-2.0-flash
* **数据模型**:
  * **User**: 基础用户信息（由Supabase Auth管理）
  * **UserProfile**: 用户档案、订阅信息、使用统计
  * **UserProject**: 用户创建的项目/解说 

### 2.2 已实现模块
* **完整的用户认证系统**: 
  * 已对接 Supabase Auth，实现了注册、登录、邮箱验证、会话管理。
  * `AuthContext.tsx` 提供了完整的认证状态管理和用户信息获取。
  * `AuthModal.tsx` 实现了登录/注册UI，支持表单验证和错误处理。
  * `AuthConfirmPage.tsx` 处理邮箱验证流程。

* **用户画像与订阅模型**: 
  * `UserProfile` 数据表已定义 `subscription_tier`, `usage_count`, `monthly_limit` 等关键字段。
  * `UserMenu.tsx` 和 `MovieDetails.tsx` 组件已实现根据用户会员等级和用量限制展示不同UI的逻辑。
  * `SubscriptionModal.tsx` 已实现三种订阅计划的展示UI。

* **核心页面流**: 
  * `HomePage.tsx`: 包含搜索和热门内容展示。
  * `DetailsPage.tsx`: 影片详情和解说工具集成页面。
  * `App.tsx`: 基础路由结构和应用框架。

* **高度仿真的UI组件**: 
  * `SearchSection.tsx`: 搜索栏和热门推荐。
  * `SearchResults.tsx`: 搜索结果展示。
  * `TrendingSection.tsx`: 热门内容展示。
  * `MovieDetails.tsx`: 影片详情和风格选择。
  * `ScriptEditor.tsx`: 解说文本编辑与展示。
  * `VideoUpload.tsx`: 视频上传与进度展示。
  * `CopyrightAnalysis.tsx`: 版权风险分析。
  * `ExportSection.tsx`: 导出功能及项目管理。

### 2.3 开发优先级与差距分析
当前项目是一个功能完善的**高保真前端原型**，已为MVP及未来阶段的功能迭代打下坚实基础。主要差距在于：

* **后端AI能力缺失**: 需要构建实际的AI解说引擎，替换当前的mock数据。
* **真实数据源集成**: 需要接入TMDB、维基百科等数据源，确保内容准确性。
* **订阅系统未完成**: 需要集成支付功能和实际的会员权限管理。
* **视频生成功能**: 需要从零构建，这是产品差异化的关键。

## 3. 产品路线图与技术需求

### 3.1 阶段一: MVP - "不死"版本 (预计1-3个月)

**目标**: 聚焦服务"小明"，验证文字版解说的核心需求，跑通基本流程，用最快速度上线收集反馈。

#### 3.1.1 影视搜索功能

| 需求项 | 详细说明 |
|---|---|
| **功能描述** | 用户输入电影名称，支持模糊搜索，返回准确的影视条目列表。点击条目进入详情页。 |
| **用户价值** | 快速找到想了解的影片，无需记住完整片名。 |
| **技术实现** | **前端**: <br>- 修改`HomePage.tsx`中的`handleSearch`函数，对接实际API。<br>- 调整`SearchResults.tsx`展示真实数据。<br>**后端**: <br>- 创建Supabase Edge Function用于代理API请求，避免暴露API密钥。<br>- 主要使用TMDB API(api.themoviedb.org)查询电影数据。<br>- 实现本地缓存层，减少重复请求。<br>- 对接Supabase缓存表，存储热门电影数据。 |
| **API参考** | ```typescript<br>// TMDB API调用示例<br>const searchMovies = async (query: string) => {<br>  const response = await fetch(<br>    `https://api.themoviedb.org/3/search/movie?query=${encodeURIComponent(query)}&api_key=${TMDB_API_KEY}`<br>  );<br>  return response.json();<br>};<br>``` |
| **技术依赖** | TMDB API密钥，Supabase Edge Functions |
| **验收标准** | - 搜索响应时间<1秒<br>- 能正确处理中英文片名<br>- 模糊搜索容错率>90% |
| **工作量估计** | 前端: 1人/天，后端: 2人/天 |

#### 3.1.2 AI解说引擎 (核心功能)

| 需求项 | 详细说明 |
|---|---|
| **功能描述** | 基于用户选择的影片，生成结构化的文字版解说报告，包含一句话总结、人物关系、情节梳理、主题分析等部分。 |
| **用户价值** | 快速了解电影内容，节省时间，提升认知效率。 |
| **技术实现** | **后端**: <br>- 创建Supabase Edge Function (`generate-commentary`)，实现以下流程：<br>1. **数据获取**: <br>   - 调用TMDB获取基础信息。<br>   - 使用维基百科API获取更详细的剧情。<br>   - 可选：爬取豆瓣/IMDb评论获取观众洞见。<br>2. **数据预处理**: <br>   - 结构化电影信息，清洗冗余数据。<br>   - 处理可能的信息冲突，确保准确性。<br>3. **AI处理**: <br>   - 调用GPT-4/Claude 3 API，构建以下Prompt Chain：<br>   - **Step 1**: 事实抓取 - 提取核心情节、人物、时间线。<br>   - **Step 2**: 逻辑重组 - 按时间或因果关系重组连贯故事。<br>   - **Step 3**: 摘要与提炼 - 生成结构化解说文本。<br>4. **数据存储**: <br>   - 将生成结果存入`UserProject`表。<br>   - 标记使用状态和版本，支持后续修改。<br>**前端**: <br>- 修改`MovieDetails.tsx`中的`handleStyleSelect`函数，真实调用上述Edge Function。<br>- 调整`ScriptEditor.tsx`展示真实生成内容。 |
| **API参考** | ```typescript<br>// Supabase Edge Function 示例<br>// /supabase/functions/generate-commentary/index.ts<br>import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'<br>import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'<br>import { Configuration, OpenAIApi } from 'https://esm.sh/openai@3.2.1'<br><br>const openaiConfig = new Configuration({<br>  apiKey: Deno.env.get('OPENAI_API_KEY'),<br>});<br>const openai = new OpenAIApi(openaiConfig);<br><br>serve(async (req) => {<br>  const { movieId, style } = await req.json()<br>  // 1. 获取电影信息<br>  // 2. 构建Prompt<br>  // 3. 调用OpenAI<br>  // 4. 存储结果<br>  return new Response(JSON.stringify({ result }), {<br>    headers: { 'Content-Type': 'application/json' },<br>  })<br>})<br>``` |
| **技术依赖** | gemini-2.0-flash API密钥，Supabase Edge Functions |
| **数据库结构** | ```sql<br>-- 在supabase/migrations/中创建:<br>create table if not exists user_projects (<br>  id uuid primary key default uuid_generate_v4(),<br>  user_id uuid references auth.users not null,<br>  media_id text not null,<br>  title text not null,<br>  original_title text,<br>  year integer,<br>  poster_url text,<br>  commentary jsonb, -- 存储结构化的解说内容<br>  style text,      -- 解说风格<br>  status text default 'completed',<br>  created_at timestamp with time zone default now(),<br>  updated_at timestamp with time zone default now()<br>);<br>``` |
| **验收标准** | - 生成响应时间<10秒<br>- 剧情准确率>99%<br>- 内容结构清晰可读 |
| **工作量估计** | 前端: 2人/天，后端: 5-7人/天 |

#### 3.1.3 解说报告展示

| 需求项 | 详细说明 |
|---|---|
| **功能描述** | 展示包含"一句话总结"、"人物关系图"、"关键情节梳理"、"核心主题"的结构化图文报告。 |
| **用户价值** | 清晰直观地了解电影内容，高效获取核心信息。 |
| **技术实现** | **前端**: <br>- 升级`ScriptEditor.tsx`组件，支持结构化内容渲染。<br>- 添加标签页导航，分别展示"总览"、"剧情"、"人物"、"主题"等内容。<br>- 使用react-flow或vis.js实现交互式人物关系图。<br>- 支持展开/折叠详细内容的交互。<br>**后端**: <br>- 无额外开发，复用AI解说引擎的输出。 |
| **UI设计参考** | - 总览部分：卡片式布局，突出核心信息。<br>- 剧情部分：时间轴式展示，可展开细节。<br>- 人物部分：关系图+角色卡片，直观展示角色关系。<br>- 主题部分：标签云+详细解析，突出电影核心价值。 |
| **技术依赖** | react-flow 或 vis.js (关系图)，react-markdown (富文本渲染) |
| **验收标准** | - 界面响应流畅<br>- 内容排版美观清晰<br>- 支持移动设备适配 |
| **工作量估计** | 前端: 3-4人/天 |

#### 3.1.4 用户用量限制

| 需求项 | 详细说明 |
|---|---|
| **功能描述** | 免费用户每月有生成次数限制(3次)，超出后提示升级。Pro/Premium用户无限制。 |
| **用户价值** | 免费尝试核心功能，引导高频用户转化为付费会员。 |
| **技术实现** | **前端**: <br>- 已部分实现，在`MovieDetails.tsx`中展示用量进度条和限制提示。<br>- 优化`SubscriptionModal.tsx`，添加实际支付功能。<br>**后端**: <br>- 强化`AuthContext.tsx`中的`incrementUsage`函数，确保每次成功生成都增加用量。<br>- 创建Supabase触发器，每月自动重置免费用户的使用次数。<br>- 创建用量统计API，支持用户查看历史使用情况。 |
| **API参考** | ```sql<br>-- 月度重置触发器<br>create or replace function reset_monthly_usage()<br>returns trigger as $$<br>begin<br>  update user_profiles<br>  set usage_count = 0<br>  where subscription_tier = 'free';<br>  return new;<br>end;<br>$$ language plpgsql;<br><br>create trigger monthly_usage_reset<br>  after insert on monthly_reset_log<br>  for each statement<br>  execute procedure reset_monthly_usage();<br>``` |
| **技术依赖** | Supabase数据库触发器，Supabase Auth Policy |
| **验收标准** | - 用量统计准确无误<br>- 限制提醒友好明确<br>- 月度重置正常执行 |
| **工作量估计** | 前端: 1人/天，后端: 2人/天 |

### 3.1.5 MVP阶段成功标准
* **性能**: 
  * 搜索响应<1秒
  * 解说生成<10秒
  * 页面加载<3秒
* **准确性**: 
  * 剧情、人物信息错误率<1%
  * 搜索准确率>95%
* **用户指标**:
  * 安装后完成1次解说生成的用户比例>50%
  * 次日留存率>10%
  * 7日留存率>5% 

### 3.2 阶段二: 进化版 - "好用"且"惊艳" (预计3-9个月)

**目标**: 满足"小明"的进阶需求和"小红"的初步需求，加入多模态输出和个性化功能，形成产品亮点。

#### 3.2.1 AI生成解说视频 (杀手级功能)

| 需求项 | 详细说明 |
|---|---|
| **功能描述** | 一键生成包含AI配音、画面、字幕的3-5分钟解说短视频。 |
| **用户价值** | 节省内容创作时间，直接获得可分享的高质量视频。 |
| **技术实现** | **后端**: <br>- 创建`generate-video` Supabase Edge Function，实现：<br>1. **AI配音生成**: <br>   - 集成ElevenLabs/Azure TTS API。<br>   - 提供5-10种不同风格和情感的声音选择。<br>   - 支持语速、语调调整。<br>2. **视频素材处理**: <br>   - **安全方案**: 爬取公开剧照、预告片，建立素材库。<br>   - **创新方案**: 集成Stability AI/Midjourney API，生成概念艺术图。<br>   - 素材标记与剧情段落匹配算法。<br>3. **视频合成**: <br>   - 使用FFmpeg进行服务端视频合成。<br>   - 生成剪映兼容的工程文件(XML)。<br>   - 提供多种模板：人物介绍型、剧情梳理型、主题分析型。<br>**前端**: <br>- 升级`ExportSection.tsx`，添加真实导出功能。<br>- 新增视频风格和模板选择界面。<br>- 新增生成进度实时反馈。 |
| **技术依赖** | ElevenLabs/Azure TTS API，Stability AI API，FFmpeg，云存储，视频处理服务器 |
| **服务架构** | 由于视频生成计算密集，需要与Supabase Edge Functions分离：<br>1. 创建独立的视频处理微服务（基于Node.js或Python）<br>2. 部署在支持GPU的云服务器上<br>3. 使用消息队列（如Redis/RabbitMQ）管理处理任务<br>4. 使用WebSocket提供实时进度反馈 |
| **数据模型扩展** | ```sql<br>-- 视频项目表<br>create table video_projects (<br>  id uuid primary key default uuid_generate_v4(),<br>  user_project_id uuid references user_projects not null,<br>  voice_id text not null,        -- 选择的声音ID<br>  template_id text not null,      -- 视频模板ID<br>  output_format text not null,    -- mp4, mov等<br>  status text default 'pending',  -- pending, processing, completed, failed<br>  output_url text,               -- 完成后的视频URL<br>  project_file_url text,         -- 剪映项目文件URL<br>  created_at timestamp with time zone default now(),<br>  updated_at timestamp with time zone default now()<br>);<br>``` |
| **验收标准** | - 视频生成时间<3分钟<br>- 配音自然度评分>4.5/5<br>- 视频流畅度无卡顿<br>- 剪映项目文件可正常导入 |
| **工作量估计** | 前端: 5人/天，后端: 15-20人/天，视频服务: 10人/天 |

#### 3.2.2 个性化解说视角

| 需求项 | 详细说明 |
|---|---|
| **功能描述** | 用户可选择"纯剧情速通"、"细节控"、"无剧透推荐"等多种解说模式。 |
| **用户价值** | 根据不同需求场景获取个性化内容，提升用户体验。 |
| **技术实现** | **前端**: <br>- 扩展`MovieDetails.tsx`中的风格选择模态框，增加视角选项。<br>- 添加视角预览说明，帮助用户理解不同视角的特点。<br>**后端**: <br>- 扩展AI解说引擎，为每种视角设计特定的Prompt模板。<br>- 模式定义：<br>  - **"纯剧情速通"**: 只关注主要情节，无分析。<br>  - **"细节控"**: 深入挖掘细节、彩蛋和隐喻。<br>  - **"无剧透推荐"**: 只介绍背景、主题和风格，不透露关键剧情。<br>  - **"导演视角"**: 分析导演手法、镜头语言和制作特点。<br>  - **"角色解析"**: 聚焦角色发展、动机和关系。 |
| **数据模型扩展** | 在user_projects表中增加perspective字段，存储用户选择的视角 |
| **验收标准** | - 不同视角内容差异明显<br>- "无剧透推荐"确实不透露关键情节<br>- 用户能清晰理解各视角特点 |
| **工作量估计** | 前端: 2人/天，后端: 3人/天 |

#### 3.2.3 交互式探索

| 需求项 | 详细说明 |
|---|---|
| **功能描述** | 用户可针对解说内容，与AI进行追问和探讨，如"为什么主角最后要那样做？" |
| **用户价值** | 深化对影片的理解，解答疑惑，获得个性化见解。 |
| **技术实现** | **前端**: <br>- 在`ScriptEditor.tsx`下方添加聊天界面组件。<br>- 实现消息流式显示、历史记录、提问建议等功能。<br>**后端**: <br>- 创建`chat-with-ai` Edge Function。<br>- 维护对话上下文，包含电影信息和解说内容。<br>- 实现RAG (检索增强生成)，使AI能检索电影相关资料回答问题。<br>- 存储对话历史，支持用户查看过往讨论。 |
| **数据模型扩展** | ```sql<br>create table conversation_history (<br>  id uuid primary key default uuid_generate_v4(),<br>  user_id uuid references auth.users not null,<br>  media_id text not null,<br>  messages jsonb not null, -- 存储对话记录<br>  created_at timestamp with time zone default now(),<br>  updated_at timestamp with time zone default now()<br>);<br>``` |
| **验收标准** | - 回答准确率>90%<br>- 回复速度<2秒<br>- 能正确识别上下文相关问题 |
| **工作量估计** | 前端: 4人/天，后端: 5人/天 |

#### 3.2.4 视频版权风险分析

| 需求项 | 详细说明 |
|---|---|
| **功能描述** | 上传视频素材后，AI分析并给出版权风险等级和规避建议。 |
| **用户价值** | 降低内容创作的法律风险，指导创作者合规使用素材。 |
| **技术实现** | **前端**: <br>- `CopyrightAnalysis.tsx`组件已预留UI，接入实际API。<br>**后端**: <br>- 创建`analyze-copyright` Edge Function。<br>- 使用计算机视觉API分析视频画面，识别关键场景。<br>- 评估连续使用时长、原声占比、是否为核心情节等因素。<br>- 根据各国法律法规和版权判例，生成风险评估和优化建议。 |
| **风险等级定义** | - **低风险**: 素材短、非核心、有明显转化<br>- **中风险**: 需注意素材时长、添加解说等<br>- **高风险**: 大量使用原片核心场景，建议替换 |
| **验收标准** | - 识别率>85%<br>- 建议的实用性和针对性<br>- 分析速度<3分钟 |
| **工作量估计** | 前端: 2人/天，后端: 8人/天 |

### 3.3 阶段三: 神级版 - "离不开"的生态护城河 (预计9个月以后)

**目标**: 深度服务高价值用户"阿B"，建立社区生态，形成网络效应和商业壁垒。

#### 3.3.1 创作者工作台 (核心变现)

| 需求项 | 详细说明 |
|---|---|
| **功能描述** | 面向内容创作者的一站式工作台，包含文案编辑、素材库、AI音色克隆、一键导出等高级功能。 |
| **用户价值** | 极大提升创作效率，从"天"缩短至"小时"，成为创作者必备工具。 |
| **技术实现** | **前端**: <br>- 新建Creator Dashboard页面，整合现有功能，并增加：<br>  - 专业文案编辑器（支持分段修改、批注、版本历史）<br>  - 素材管理系统（上传、标签、智能搜索）<br>  - 项目管理面板（草稿、历史记录、团队协作）<br>  - 平台发布中心（一键发布到B站、抖音等平台）<br>**后端**: <br>- 升级视频生成服务，支持更精细的控制和优化<br>- 创建素材库服务，管理用户上传的素材和AI生成资源<br>- 集成到主流视频平台的API，实现一键发布<br>- 音色克隆服务，支持基于用户声音样本生成定制AI音色 |
| **数据模型扩展** | 创建creator_projects, media_assets, voice_models等多个表，支持复杂的创作者工作流 |
| **验收标准** | - 工作流程完整无障碍<br>- 支持至少3个主流平台发布<br>- 克隆音色相似度>90% |
| **工作量估计** | 前端: 20人/天，后端: 30人/天 |

#### 3.3.2 开放式知识库 & 社区

| 需求项 | 详细说明 |
|---|---|
| **功能描述** | 用户可对AI解说进行"事实核查"和"补充观点"，优质贡献者获奖励，形成高质量UGC社区。 |
| **用户价值** | 获取更准确、多元的影片解析，参与社区互动增强粘性。 |
| **技术实现** | **前端**: <br>- 新建社区页面，包含讨论区、知识库、排行榜等<br>- 实现贡献系统UI（编辑、审核、积分）<br>**后端**: <br>- 创建社区服务，管理用户贡献和互动<br>- 设计积分和声望系统，激励高质量内容<br>- 实现内容审核机制，确保质量和合规<br>- 内容推荐算法，基于用户兴趣和质量评分 |
| **社区运营机制** | - **贡献等级**: 从新手到专家的进阶路径<br>- **奖励机制**: 积分、会员折扣、定制功能<br>- **专题活动**: 特定导演/题材的协作解析 |
| **验收标准** | - 月活跃贡献者>1000<br>- 内容质量评分>4.5/5<br>- 社区互动率>20% |
| **工作量估计** | 前端: 15人/天，后端: 20人/天 |

#### 3.3.3 全平台覆盖

| 需求项 | 详细说明 |
|---|---|
| **功能描述** | 将产品从Web扩展到小程序、APP、桌面客户端等多平台。 |
| **用户价值** | 随时随地使用，提升便捷性和到达率。 |
| **技术实现** | **技术选型**: <br>- 移动端: React Native或Flutter<br>- 桌面端: Electron或Tauri<br>- 小程序: 原生开发<br>**核心考量**: <br>- 统一的后端API<br>- 跨平台的设计系统<br>- 数据同步机制<br>- 离线功能支持 |
| **功能优先级** | 1. 基础浏览和解说生成<br>2. 视频播放和分享<br>3. 高级编辑功能 |
| **验收标准** | - 核心功能跨平台一致<br>- 性能流畅度接近原生<br>- 各平台特性适配良好 |
| **工作量估计** | 移动端: 40人/天，桌面端: 30人/天，小程序: 25人/天 |

## 4. 商业模式与技术架构

### 4.1 商业模式

#### 4.1.1 收入来源

| 计划名称 | 价格 | 功能 | 目标用户 |
|---|---|---|---|
| **免费版** | ¥0/永久 | - 每月3次文字解说<br>- 基础AI配音<br>- 标准导出格式<br>- 社区访问权限 | 尝鲜用户，小明的轻度需求 |
| **Pro版** | ¥99/月 | - 无限次文字解说<br>- 无限次视频生成<br>- 所有AI配音风格<br>- 高清无水印导出<br>- 交互式问答无限制<br>- 优先客服支持 | 小明和小红，高频使用者 |
| **Premium版** | ¥199/月 | - Pro版全部功能<br>- 创作者工作台完整功能<br>- AI语音克隆<br>- 专属素材库<br>- 一键发布到各平台<br>- API访问权限<br>- 团队协作功能 | 阿B，专业内容创作者 |
| **企业版** | 定制 | - Premium全功能<br>- 定制化开发<br>- 专属客户经理<br>- SLA保障<br>- 白标服务 | MCN机构，专业制作公司 |

#### 4.1.2 支付与订阅系统

**技术实现**:
- 集成Stripe作为主要支付处理商（国际用户）
- 集成微信支付和支付宝（国内用户）
- 创建Supabase Edge Function处理支付Webhook
- 设计订阅状态管理和自动续费机制
- 实现降级/升级逻辑和按比例计费

**会员权益管理**:
- 基于RLS（行级安全）的功能访问控制
- 用量监控和限制系统
- 会员专属功能标记与引导

### 4.2 技术架构

#### 4.2.1 前端架构

```
├── 前端应用
│   ├── 核心页面
│   │   ├── HomePage - 首页和搜索
│   │   ├── DetailsPage - 影片详情和解说
│   │   ├── CreatorStudio - 创作者工作台
│   │   └── Community - 社区和知识库
│   ├── 组件库
│   │   ├── UI组件 - 按钮、表单、卡片等
│   │   ├── 业务组件 - 解说编辑器、视频播放器等
│   │   └── 布局组件 - 页面结构、响应式容器
│   ├── 全局状态管理
│   │   ├── 用户状态 (AuthContext)
│   │   ├── 项目状态
│   │   └── UI状态
│   └── 服务层
│       ├── API客户端
│       ├── 数据转换
│       └── 缓存管理
```

**技术栈细节**:
- React 18 + TypeScript 5
- 状态管理: React Context + Hooks
- 样式: TailwindCSS + CSS Modules
- 构建: Vite
- 测试: Jest + React Testing Library
- CI/CD: GitHub Actions

#### 4.2.2 后端架构

```
├── Supabase服务
│   ├── 认证服务
│   │   ├── 用户注册/登录
│   │   ├── 社交登录
│   │   └── 权限管理
│   ├── 数据库服务
│   │   ├── 用户数据
│   │   ├── 项目数据
│   │   ├── 媒体资产
│   │   └── 社区内容
│   ├── Edge Functions
│   │   ├── generate-commentary - 生成解说
│   │   ├── chat-with-ai - 交互式问答
│   │   ├── analyze-copyright - 版权分析
│   │   └── payment-webhook - 支付处理
│   └── 存储服务
│       ├── 用户上传
│       └── 生成资产
├── 视频处理服务 (独立微服务)
│   ├── 队列管理
│   ├── 视频合成
│   ├── 音频处理
│   └── 导出服务
└── AI服务集成
    ├── OpenAI/Claude - 文本生成
    ├── ElevenLabs - 语音合成
    ├── Stability AI - 图像生成
    └── 自定义模型 - 特定任务优化
```

**技术栈细节**:
- Supabase (PostgreSQL, Auth, Storage, Edge Functions)
- 视频服务: Node.js/Python, FFmpeg, Redis
- AI集成: OpenAI API, ElevenLabs API, Stability AI API
- 云基础设施: Vercel (前端), Supabase (后端), AWS/GCP (视频处理)

### 4.3 数据安全与合规

#### 4.3.1 数据保护措施
- 全站HTTPS加密
- JWT用于API认证
- 敏感数据加密存储
- 定期安全审计
- 数据备份与灾难恢复

#### 4.3.2 内容合规保障
- AI内容审核机制
- 用户举报系统
- 版权风险自动评估
- DMCA投诉处理流程
- 内容合规定期巡检

#### 4.3.3 隐私合规
- 符合GDPR和CCPA要求
- 清晰的隐私政策
- 用户数据导出功能
- 账号删除机制
- 数据留存期管理

## 5. 推广与增长策略

### 5.1 冷启动策略
- 在豆瓣、知乎、Reddit等影迷社区进行软性分享
- 邀请50-100位种子用户参与内测
- 收集反馈并快速迭代
- 制作产品Demo视频展示核心价值

### 5.2 内容营销
- 官方账号发布高质量AI生成解说
- 制作"经典电影的N种解读"系列内容
- 行业洞察报告与影视分析文章
- 与影评类公众号/博主合作

### 5.3 KOL合作
- 邀请影视区UP主试用并分享体验
- 与电影评论家合作生成专业解析
- 赞助小型影视创作者
- 推出创作者推荐计划

### 5.4 增长黑客策略
- 社交分享功能（生成解说后一键分享）
- 推荐机制（邀请好友获额外使用次数）
- SEO优化（针对电影名称和热门解说关键词）
- 限时活动（新片上映同步解说活动）

## 6. 里程碑与验收标准

### 6.1 阶段一: MVP (1-3个月)
- 周1-2: 接入TMDB API，完成搜索功能
- 周3-4: 构建基础AI解说引擎
- 周5-6: 完成解说展示页面
- 周7-8: 实现用户系统和用量限制
- 周9-10: 内部测试和Bug修复
- 周11-12: 邀请内测和产品优化

**验收指标**:
- 日活跃用户: 100+
- 周留存率: 30%+
- 解说生成成功率: 95%+

### 6.2 阶段二: 进化版 (3-9个月)
- 月1-2: 构建视频生成核心服务
- 月3: 实现多种解说视角
- 月4: 开发交互式探索功能
- 月5: 实现版权风险分析
- 月6: 整合功能，优化体验

**验收指标**:
- 日活跃用户: 1,000+
- 付费转化率: 5%+
- 视频生成使用率: 30%+

### 6.3 阶段三: 神级版 (9个月以后)
- 创作者工作台完整上线
- 社区功能全面铺开
- 多平台应用发布

**验收指标**:
- 日活跃用户: 10,000+
- 付费转化率: 8%+
- 社区活跃度: 20%+
- 创作者专业版占比: 2%+ 