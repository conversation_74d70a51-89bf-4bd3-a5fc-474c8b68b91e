# NarratoAI Edge Functions

本目录包含了NarratoAI项目的Supabase Edge Functions，用于处理影视数据搜索和获取详情等功能。

## 📁 目录结构

```
functions/
├── _shared/           # 共享工具和配置
│   ├── cors.ts       # CORS配置和响应处理
│   ├── utils.ts      # 通用工具函数
│   └── cache.ts      # 内存缓存机制
├── _test/            # 测试脚本
│   └── test-functions.ts
├── movie-search/     # 影视搜索功能
│   └── index.ts
├── movie-details/    # 影视详情获取
│   └── index.ts
└── README.md
```

## 🚀 功能说明

### 1. movie-search - 影视搜索

**功能**: 基于TMDB API的影视搜索功能，支持电影和电视剧搜索。

**端点**: `/functions/v1/movie-search`

**支持的HTTP方法**: GET, POST

**参数**:
- `query` (必需): 搜索关键词
- `page` (可选): 页码，默认为1
- `type` (可选): 搜索类型，可选值: `movie`, `tv`, `multi`，默认为`multi`
- `language` (可选): 语言代码，默认为`zh-CN`
- `include_adult` (可选): 是否包含成人内容，默认为`false`

**GET请求示例**:
```bash
curl "https://your-project.supabase.co/functions/v1/movie-search?query=流浪地球&page=1&type=movie"
```

**POST请求示例**:
```bash
curl -X POST "https://your-project.supabase.co/functions/v1/movie-search" \
  -H "Content-Type: application/json" \
  -d '{
    "query": "流浪地球",
    "page": 1,
    "type": "movie",
    "language": "zh-CN"
  }'
```

**响应格式**:
```json
{
  "success": true,
  "data": {
    "page": 1,
    "results": [
      {
        "id": 535167,
        "title": "流浪地球",
        "original_title": "流浪地球",
        "overview": "电影简介...",
        "poster_url": "https://image.tmdb.org/t/p/w500/poster.jpg",
        "backdrop_url": "https://image.tmdb.org/t/p/w1280/backdrop.jpg",
        "release_date": "2019-02-05",
        "vote_average": 6.645,
        "vote_count": 1028,
        "popularity": 4.8716,
        "genre_ids": [878, 28, 18],
        "media_type": "movie",
        "language": "zh"
      }
    ],
    "total_pages": 1,
    "total_results": 5,
    "query": "流浪地球"
  },
  "cached": false,
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

### 2. movie-details - 影视详情

**功能**: 获取电影或电视剧的详细信息，包括演员、导演、制作公司等。

**端点**: `/functions/v1/movie-details`

**支持的HTTP方法**: GET

**参数**:
- `id` (必需): 电影或电视剧的TMDB ID
- `type` (可选): 媒体类型，可选值: `movie`, `tv`，默认为`movie`

**请求示例**:
```bash
curl "https://your-project.supabase.co/functions/v1/movie-details?id=535167&type=movie"
```

**响应格式**:
```json
{
  "success": true,
  "data": {
    "id": 535167,
    "title": "流浪地球",
    "original_title": "流浪地球",
    "overview": "电影简介...",
    "poster_url": "https://image.tmdb.org/t/p/w500/poster.jpg",
    "backdrop_url": "https://image.tmdb.org/t/p/w1280/backdrop.jpg",
    "release_date": "2019-02-05",
    "vote_average": 6.645,
    "vote_count": 1028,
    "genres": [
      {"id": 878, "name": "科幻"},
      {"id": 28, "name": "动作"}
    ],
    "runtime": 126,
    "cast": [
      {
        "id": 1234,
        "name": "吴京",
        "character": "刘培强",
        "profile_url": "https://image.tmdb.org/t/p/w185/profile.jpg",
        "order": 0
      }
    ],
    "crew": {
      "directors": [
        {
          "id": 5678,
          "name": "郭帆",
          "profile_url": "https://image.tmdb.org/t/p/w185/profile.jpg"
        }
      ],
      "writers": [],
      "producers": []
    },
    "production_companies": [],
    "media_type": "movie"
  },
  "cached": false,
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

## ⚙️ 环境变量配置

在使用这些Edge Functions之前，需要配置以下环境变量：

```bash
# TMDB API密钥（必需）
TMDB_API_KEY=your_tmdb_api_key_here

# Supabase配置（通常自动提供）
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_ANON_KEY=your_anon_key_here
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key_here
```

## 🧪 测试

运行测试脚本来验证Edge Functions的功能：

```bash
# 确保已配置环境变量
export SUPABASE_URL="http://127.0.0.1:54321"  # 本地开发
export SUPABASE_ANON_KEY="your-anon-key"
export TMDB_API_KEY="your-tmdb-api-key"

# 运行测试
deno run --allow-net --allow-env supabase/functions/_test/test-functions.ts
```

## 🚀 部署

使用Supabase CLI部署Edge Functions：

```bash
# 部署所有函数
supabase functions deploy

# 部署特定函数
supabase functions deploy movie-search
supabase functions deploy movie-details

# 设置环境变量
supabase secrets set TMDB_API_KEY=your_tmdb_api_key_here
```

## 📊 性能特性

### 缓存机制
- **搜索结果缓存**: 5分钟
- **详情数据缓存**: 10分钟
- **自动清理**: 每分钟清理过期缓存

### 错误处理
- 自动重试机制（搜索3次，详情2次）
- 指数退避策略
- 详细的错误分类和响应

### CORS支持
- 支持跨域请求
- 预检请求处理
- 标准化响应头

## 🔧 开发指南

### 添加新的Edge Function

1. 在`functions/`目录下创建新文件夹
2. 创建`index.ts`文件作为入口点
3. 使用`_shared/`中的工具函数
4. 添加相应的测试用例

### 最佳实践

1. **错误处理**: 使用`createErrorResponse`创建标准化错误响应
2. **CORS**: 使用`handleCors`处理跨域请求
3. **缓存**: 合理使用缓存减少API调用
4. **日志**: 使用`logRequest`记录请求信息
5. **重试**: 对外部API调用使用`retry`函数

## 📝 更新日志

- **v1.0.0**: 初始版本，包含电影搜索和详情获取功能
- 支持TMDB API集成
- 内置缓存机制
- 完整的错误处理和CORS支持
