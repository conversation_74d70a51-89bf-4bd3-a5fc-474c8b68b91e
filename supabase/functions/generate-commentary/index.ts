import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'
import { handleCors, createCorsResponse, createErrorResponse } from '../_shared/cors.ts'
import { logRequest, validateMethod } from '../_shared/utils.ts'

// 环境变量
const SUPABASE_URL = Deno.env.get('SUPABASE_URL')!
const SUPABASE_SERVICE_ROLE_KEY = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!
const OPENAI_API_KEY = Deno.env.get('OPENAI_API_KEY')

// 创建Supabase客户端
const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY)

/**
 * 解说风格配置
 */
const COMMENTARY_STYLES = {
  passionate: {
    name: '激情吐槽',
    prompt: '请以幽默风趣、槽点满满的风格来解说这部电影，语言要生动有趣，适当加入网络流行语和梗，让观众在笑声中了解电影内容。'
  },
  analytical: {
    name: '深度解析',
    prompt: '请以专业分析、见解独到的风格来解说这部电影，深入挖掘电影的主题、象征意义和艺术价值，提供有深度的观点和分析。'
  },
  suspense: {
    name: '悬疑故事',
    prompt: '请以引人入胜、扣人心弦的风格来解说这部电影，营造悬疑氛围，逐步揭示情节，保持观众的好奇心和紧张感。'
  },
  emotional: {
    name: '情感共鸣',
    prompt: '请以温暖治愈、触动人心的风格来解说这部电影，关注人物的情感变化和内心世界，引发观众的情感共鸣。'
  }
}

/**
 * 生成AI解说文案
 */
async function generateCommentaryWithAI(movieData: any, style: string): Promise<string> {
  if (!OPENAI_API_KEY) {
    console.warn('⚠️ OpenAI API Key未配置，使用模拟数据')
    return generateMockCommentary(movieData, style)
  }

  const styleConfig = COMMENTARY_STYLES[style as keyof typeof COMMENTARY_STYLES]
  if (!styleConfig) {
    throw new Error('不支持的解说风格')
  }

  const prompt = `
你是一位专业的电影解说员，请根据以下电影信息生成一份精彩的解说文案：

电影信息：
- 标题：${movieData.title}
- 原标题：${movieData.original_title || movieData.title}
- 年份：${movieData.year}
- 类型：${movieData.genres?.join('、') || '未知'}
- 导演：${movieData.director || '未知'}
- 主演：${movieData.actors?.join('、') || '未知'}
- 评分：${movieData.rating || '未知'}
- 剧情简介：${movieData.plot_summary || '暂无简介'}

解说风格要求：
${styleConfig.prompt}

请生成一份结构化的解说文案，包含以下部分：
1. 开场白（吸引观众注意）
2. 背景介绍（电影基本信息和背景）
3. 情节梳理（主要故事线和关键情节）
4. 人物分析（主要角色和演员表现）
5. 主题探讨（电影想要表达的主题和意义）
6. 总结评价（整体评价和推荐理由）

要求：
- 每个部分用<p>标签包裹
- 语言生动有趣，符合所选风格
- 内容要有逻辑性和连贯性
- 总字数控制在800-1200字之间
- 避免剧透关键情节
`

  try {
    const response = await fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${OPENAI_API_KEY}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        model: 'gpt-3.5-turbo',
        messages: [
          {
            role: 'system',
            content: '你是一位专业的电影解说员，擅长用不同风格解说电影，语言生动有趣。'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        max_tokens: 2000,
        temperature: 0.8
      })
    })

    if (!response.ok) {
      throw new Error(`OpenAI API调用失败: ${response.status}`)
    }

    const data = await response.json()
    
    if (!data.choices || !data.choices[0] || !data.choices[0].message) {
      throw new Error('OpenAI API返回数据格式错误')
    }

    return data.choices[0].message.content.trim()
  } catch (error) {
    console.error('❌ OpenAI API调用失败:', error)
    console.log('🔄 降级使用模拟数据')
    return generateMockCommentary(movieData, style)
  }
}

/**
 * 生成模拟解说文案（当AI API不可用时使用）
 */
function generateMockCommentary(movieData: any, style: string): string {
  const styleConfig = COMMENTARY_STYLES[style as keyof typeof COMMENTARY_STYLES]
  const styleName = styleConfig?.name || '标准解说'

  return `
<p>【${styleName}风格解说】欢迎来到今天的电影解说，今天我们要聊的是这部${movieData.genres?.join('、') || '精彩'}电影——《${movieData.title}》。</p>

<p>这部由${movieData.director || '知名导演'}执导的作品，汇集了${movieData.actors?.slice(0, 3).join('、') || '众多实力派演员'}等实力派演员，在${movieData.year || '近年来'}上映后获得了${movieData.rating || '不错的'}的评分。</p>

<p>故事的核心围绕着${movieData.plot_summary || '一个引人入胜的故事'}展开。电影通过精彩的叙事和出色的表演，为我们呈现了一个充满张力的故事世界。</p>

<p>从人物塑造的角度来看，主要角色们都有着鲜明的个性特征。${movieData.actors?.[0] || '主演'}的表演尤其出彩，将角色的内心世界展现得淋漓尽致。</p>

<p>这部电影不仅仅是一部娱乐作品，更是对${movieData.genres?.[0] || '人性'}主题的深度探讨。导演通过巧妙的镜头语言和情节设计，让观众在观影过程中思考更深层次的问题。</p>

<p>总的来说，《${movieData.title}》是一部值得一看的${movieData.genres?.[0] || '优秀'}电影。无论你是${movieData.genres?.[0] || '电影'}爱好者，还是普通观众，都能从中找到属于自己的观影乐趣。如果你还没有看过这部电影，强烈推荐你去体验一下。</p>
`
}

/**
 * 保存解说文案到数据库
 */
async function saveCommentaryToDatabase(userId: string, movieData: any, commentary: string, style: string) {
  const narrationData = {
    user_id: userId,
    media_id: movieData.media_id?.toString() || movieData.id?.toString(),
    title: movieData.title,
    script_content: commentary,
    status: 'completed'
  }

  const { data, error } = await supabase
    .from('user_projects')
    .insert(narrationData)
    .select()
    .single()

  if (error) {
    console.error('❌ 保存解说文案失败:', error)
    throw new Error('保存解说文案失败')
  }

  console.log('✅ 解说文案已保存到数据库:', data.id)
  return data
}

/**
 * 主处理函数
 */
Deno.serve(async (req: Request) => {
  try {
    // 处理CORS预检请求
    const corsResponse = handleCors(req)
    if (corsResponse) return corsResponse

    // 记录请求
    logRequest(req, 'generate-commentary')

    // 验证请求方法
    validateMethod(req, ['POST'])

    // 获取请求体
    const body = await req.json()
    const { movie_id, movie_title, movie_data, style } = body

    // 验证必需参数
    if (!movie_id || !movie_title || !movie_data || !style) {
      return createErrorResponse('缺少必需参数：movie_id, movie_title, movie_data, style', 400)
    }

    // 验证解说风格
    if (!COMMENTARY_STYLES[style as keyof typeof COMMENTARY_STYLES]) {
      return createErrorResponse('不支持的解说风格', 400)
    }

    console.log(`🎬 开始生成解说文案: ${movie_title} (${movie_id}), 风格: ${style}`)

    // 获取用户信息（从JWT token中）
    const authHeader = req.headers.get('Authorization')
    if (!authHeader) {
      return createErrorResponse('未提供认证信息', 401)
    }

    // 验证JWT token并获取用户信息
    const token = authHeader.replace('Bearer ', '')
    const { data: { user }, error: authError } = await supabase.auth.getUser(token)

    if (authError || !user) {
      console.error('❌ 用户认证失败:', authError)
      return createErrorResponse('用户认证失败', 401)
    }

    const userId = user.id

    // 生成解说文案
    const commentary = await generateCommentaryWithAI(movie_data, style)

    // 保存到数据库
    const savedData = await saveCommentaryToDatabase(userId, movie_data, commentary, style)

    // 返回结果
    return createCorsResponse({
      success: true,
      data: {
        narration_id: savedData.id,
        content_html: commentary,
        status: 'completed'
      },
      message: '解说文案生成成功'
    })

  } catch (error) {
    console.error('❌ 生成解说文案失败:', error)
    return createErrorResponse(
      error instanceof Error ? error.message : '生成解说文案失败',
      500
    )
  }
})
