/**
 * Edge Functions测试脚本
 * 用于测试movie-search和movie-details函数的功能
 */

// 测试配置
const SUPABASE_URL = Deno.env.get('SUPABASE_URL') || 'http://127.0.0.1:54321'
const SUPABASE_ANON_KEY = Deno.env.get('SUPABASE_ANON_KEY') || 'your-anon-key'

interface TestResult {
  name: string
  success: boolean
  duration: number
  error?: string
  data?: any
}

/**
 * 执行HTTP请求
 */
async function makeRequest(
  url: string, 
  options: RequestInit = {}
): Promise<{ status: number; data: any; duration: number }> {
  const startTime = Date.now()
  
  const response = await fetch(url, {
    headers: {
      'Authorization': `Bearer ${SUPABASE_ANON_KEY}`,
      'Content-Type': 'application/json',
      ...options.headers
    },
    ...options
  })
  
  const duration = Date.now() - startTime
  const data = await response.json()
  
  return {
    status: response.status,
    data,
    duration
  }
}

/**
 * 测试电影搜索功能
 */
async function testMovieSearch(): Promise<TestResult[]> {
  const tests: TestResult[] = []
  
  // 测试1: 基本搜索功能
  try {
    const result = await makeRequest(
      `${SUPABASE_URL}/functions/v1/movie-search?query=流浪地球&page=1&type=movie&language=zh-CN`
    )
    
    tests.push({
      name: '电影搜索 - 基本功能',
      success: result.status === 200 && result.data.success && result.data.data.results.length > 0,
      duration: result.duration,
      data: result.data
    })
  } catch (error) {
    tests.push({
      name: '电影搜索 - 基本功能',
      success: false,
      duration: 0,
      error: error.message
    })
  }
  
  // 测试2: POST请求搜索
  try {
    const result = await makeRequest(
      `${SUPABASE_URL}/functions/v1/movie-search`,
      {
        method: 'POST',
        body: JSON.stringify({
          query: '复仇者联盟',
          page: 1,
          type: 'movie',
          language: 'zh-CN'
        })
      }
    )
    
    tests.push({
      name: '电影搜索 - POST请求',
      success: result.status === 200 && result.data.success,
      duration: result.duration,
      data: result.data
    })
  } catch (error) {
    tests.push({
      name: '电影搜索 - POST请求',
      success: false,
      duration: 0,
      error: error.message
    })
  }
  
  // 测试3: 缓存功能（第二次相同请求应该更快）
  try {
    const firstResult = await makeRequest(
      `${SUPABASE_URL}/functions/v1/movie-search?query=阿凡达&page=1&type=movie&language=zh-CN`
    )
    
    const secondResult = await makeRequest(
      `${SUPABASE_URL}/functions/v1/movie-search?query=阿凡达&page=1&type=movie&language=zh-CN`
    )
    
    tests.push({
      name: '电影搜索 - 缓存功能',
      success: firstResult.status === 200 && secondResult.status === 200 && 
               secondResult.data.cached === true && secondResult.duration < firstResult.duration,
      duration: secondResult.duration,
      data: { first: firstResult.duration, second: secondResult.duration, cached: secondResult.data.cached }
    })
  } catch (error) {
    tests.push({
      name: '电影搜索 - 缓存功能',
      success: false,
      duration: 0,
      error: error.message
    })
  }
  
  // 测试4: 错误处理 - 空查询
  try {
    const result = await makeRequest(
      `${SUPABASE_URL}/functions/v1/movie-search?query=&page=1`
    )
    
    tests.push({
      name: '电影搜索 - 错误处理（空查询）',
      success: result.status === 400,
      duration: result.duration,
      data: result.data
    })
  } catch (error) {
    tests.push({
      name: '电影搜索 - 错误处理（空查询）',
      success: false,
      duration: 0,
      error: error.message
    })
  }
  
  return tests
}

/**
 * 测试电影详情功能
 */
async function testMovieDetails(): Promise<TestResult[]> {
  const tests: TestResult[] = []
  
  // 测试1: 获取电影详情
  try {
    const result = await makeRequest(
      `${SUPABASE_URL}/functions/v1/movie-details?id=535167&type=movie`
    )
    
    tests.push({
      name: '电影详情 - 基本功能',
      success: result.status === 200 && result.data.success && result.data.data.id === 535167,
      duration: result.duration,
      data: result.data
    })
  } catch (error) {
    tests.push({
      name: '电影详情 - 基本功能',
      success: false,
      duration: 0,
      error: error.message
    })
  }
  
  // 测试2: 获取电视剧详情
  try {
    const result = await makeRequest(
      `${SUPABASE_URL}/functions/v1/movie-details?id=1399&type=tv`
    )
    
    tests.push({
      name: '电影详情 - 电视剧',
      success: result.status === 200 && result.data.success && result.data.data.media_type === 'tv',
      duration: result.duration,
      data: result.data
    })
  } catch (error) {
    tests.push({
      name: '电影详情 - 电视剧',
      success: false,
      duration: 0,
      error: error.message
    })
  }
  
  // 测试3: 缓存功能
  try {
    const firstResult = await makeRequest(
      `${SUPABASE_URL}/functions/v1/movie-details?id=550&type=movie`
    )
    
    const secondResult = await makeRequest(
      `${SUPABASE_URL}/functions/v1/movie-details?id=550&type=movie`
    )
    
    tests.push({
      name: '电影详情 - 缓存功能',
      success: firstResult.status === 200 && secondResult.status === 200 && 
               secondResult.data.cached === true,
      duration: secondResult.duration,
      data: { first: firstResult.duration, second: secondResult.duration, cached: secondResult.data.cached }
    })
  } catch (error) {
    tests.push({
      name: '电影详情 - 缓存功能',
      success: false,
      duration: 0,
      error: error.message
    })
  }
  
  // 测试4: 错误处理 - 不存在的ID
  try {
    const result = await makeRequest(
      `${SUPABASE_URL}/functions/v1/movie-details?id=999999999&type=movie`
    )
    
    tests.push({
      name: '电影详情 - 错误处理（不存在的ID）',
      success: result.status === 404,
      duration: result.duration,
      data: result.data
    })
  } catch (error) {
    tests.push({
      name: '电影详情 - 错误处理（不存在的ID）',
      success: false,
      duration: 0,
      error: error.message
    })
  }
  
  return tests
}

/**
 * 运行所有测试
 */
async function runAllTests(): Promise<void> {
  console.log('🚀 开始测试 Supabase Edge Functions...\n')
  
  console.log('📽️  测试电影搜索功能...')
  const searchTests = await testMovieSearch()
  
  console.log('\n🎬 测试电影详情功能...')
  const detailsTests = await testMovieDetails()
  
  // 汇总结果
  const allTests = [...searchTests, ...detailsTests]
  const passedTests = allTests.filter(test => test.success)
  const failedTests = allTests.filter(test => !test.success)
  
  console.log('\n📊 测试结果汇总:')
  console.log('='.repeat(50))
  
  allTests.forEach(test => {
    const status = test.success ? '✅' : '❌'
    const duration = test.duration ? `(${test.duration}ms)` : ''
    console.log(`${status} ${test.name} ${duration}`)
    
    if (!test.success && test.error) {
      console.log(`   错误: ${test.error}`)
    }
  })
  
  console.log('='.repeat(50))
  console.log(`总计: ${allTests.length} 个测试`)
  console.log(`通过: ${passedTests.length} 个`)
  console.log(`失败: ${failedTests.length} 个`)
  console.log(`成功率: ${((passedTests.length / allTests.length) * 100).toFixed(1)}%`)
  
  if (failedTests.length > 0) {
    console.log('\n❌ 失败的测试:')
    failedTests.forEach(test => {
      console.log(`- ${test.name}: ${test.error || '未知错误'}`)
    })
  }
  
  console.log('\n🎉 测试完成!')
}

// 运行测试
if (import.meta.main) {
  await runAllTests()
}
