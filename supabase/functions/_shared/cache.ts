/**
 * 简单的内存缓存机制
 * 用于缓存API响应，减少外部API调用
 */

interface CacheItem<T> {
  data: T
  timestamp: number
  ttl: number
}

class SimpleCache<T> {
  private cache = new Map<string, CacheItem<T>>()
  private defaultTTL: number

  constructor(defaultTTL: number = 300000) { // 默认5分钟
    this.defaultTTL = defaultTTL
  }

  /**
   * 设置缓存
   * @param key - 缓存键
   * @param data - 缓存数据
   * @param ttl - 过期时间（毫秒），可选
   */
  set(key: string, data: T, ttl?: number): void {
    const item: CacheItem<T> = {
      data,
      timestamp: Date.now(),
      ttl: ttl || this.defaultTTL
    }
    this.cache.set(key, item)
  }

  /**
   * 获取缓存
   * @param key - 缓存键
   * @returns 缓存数据或null
   */
  get(key: string): T | null {
    const item = this.cache.get(key)
    if (!item) {
      return null
    }

    // 检查是否过期
    if (Date.now() - item.timestamp > item.ttl) {
      this.cache.delete(key)
      return null
    }

    return item.data
  }

  /**
   * 删除缓存
   * @param key - 缓存键
   */
  delete(key: string): boolean {
    return this.cache.delete(key)
  }

  /**
   * 清空所有缓存
   */
  clear(): void {
    this.cache.clear()
  }

  /**
   * 获取缓存大小
   */
  size(): number {
    return this.cache.size
  }

  /**
   * 清理过期缓存
   */
  cleanup(): number {
    const now = Date.now()
    let cleaned = 0

    for (const [key, item] of this.cache.entries()) {
      if (now - item.timestamp > item.ttl) {
        this.cache.delete(key)
        cleaned++
      }
    }

    return cleaned
  }

  /**
   * 获取或设置缓存（如果不存在则执行函数并缓存结果）
   * @param key - 缓存键
   * @param fn - 获取数据的函数
   * @param ttl - 过期时间（毫秒），可选
   * @returns 缓存数据
   */
  async getOrSet(key: string, fn: () => Promise<T>, ttl?: number): Promise<T> {
    const cached = this.get(key)
    if (cached !== null) {
      return cached
    }

    const data = await fn()
    this.set(key, data, ttl)
    return data
  }
}

// 创建全局缓存实例
export const searchCache = new SimpleCache<any>(300000) // 5分钟
export const detailsCache = new SimpleCache<any>(600000) // 10分钟

// 定期清理过期缓存
setInterval(() => {
  const searchCleaned = searchCache.cleanup()
  const detailsCleaned = detailsCache.cleanup()
  
  if (searchCleaned > 0 || detailsCleaned > 0) {
    console.log(`Cache cleanup: ${searchCleaned} search items, ${detailsCleaned} details items`)
  }
}, 60000) // 每分钟清理一次

/**
 * 生成搜索缓存键
 */
export function generateSearchCacheKey(query: string, page: number, type: string, language: string): string {
  return `search:${type}:${language}:${page}:${query.toLowerCase().trim()}`
}

/**
 * 生成详情缓存键
 */
export function generateDetailsCacheKey(id: string, type: string, language: string): string {
  return `details:${type}:${language}:${id}`
}
