/**
 * 通用工具函数 - 用于所有Edge Functions
 */

/**
 * 验证环境变量是否存在
 * @param name - 环境变量名称
 * @returns 环境变量值
 * @throws 如果环境变量不存在则抛出错误
 */
export function getRequiredEnv(name: string): string {
  const value = Deno.env.get(name)
  if (!value) {
    throw new Error(`Missing required environment variable: ${name}`)
  }
  return value
}

/**
 * 安全地解析JSON请求体
 * @param req - 请求对象
 * @returns 解析后的JSON对象
 */
export async function safeParseJson(req: Request): Promise<any> {
  try {
    const text = await req.text()
    if (!text.trim()) {
      return {}
    }
    return JSON.parse(text)
  } catch (error) {
    throw new Error('Invalid JSON in request body')
  }
}

/**
 * 验证请求方法
 * @param req - 请求对象
 * @param allowedMethods - 允许的HTTP方法数组
 * @throws 如果方法不被允许则抛出错误
 */
export function validateMethod(req: Request, allowedMethods: string[]): void {
  if (!allowedMethods.includes(req.method)) {
    throw new Error(`Method ${req.method} not allowed. Allowed methods: ${allowedMethods.join(', ')}`)
  }
}

/**
 * 记录请求信息（用于调试）
 * @param req - 请求对象
 * @param functionName - 函数名称
 */
export function logRequest(req: Request, functionName: string): void {
  const url = new URL(req.url)
  console.log(`[${functionName}] ${req.method} ${url.pathname}${url.search}`)
}

/**
 * 创建缓存键
 * @param prefix - 前缀
 * @param params - 参数对象
 * @returns 缓存键字符串
 */
export function createCacheKey(prefix: string, params: Record<string, any>): string {
  const sortedParams = Object.keys(params)
    .sort()
    .map(key => `${key}=${params[key]}`)
    .join('&')
  return `${prefix}:${sortedParams}`
}

/**
 * 延迟执行
 * @param ms - 延迟毫秒数
 */
export function delay(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms))
}

/**
 * 重试机制
 * @param fn - 要重试的函数
 * @param maxRetries - 最大重试次数
 * @param delayMs - 重试间隔（毫秒）
 * @returns 函数执行结果
 */
export async function retry<T>(
  fn: () => Promise<T>,
  maxRetries: number = 3,
  delayMs: number = 1000
): Promise<T> {
  let lastError: Error
  
  for (let i = 0; i <= maxRetries; i++) {
    try {
      return await fn()
    } catch (error) {
      lastError = error as Error
      if (i < maxRetries) {
        console.warn(`Retry ${i + 1}/${maxRetries} failed:`, error)
        await delay(delayMs * Math.pow(2, i)) // 指数退避
      }
    }
  }
  
  throw lastError!
}
