/**
 * CORS配置 - 用于所有Edge Functions
 * 支持跨域请求，确保前端能够正确调用API
 */

export const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  'Access-Control-Allow-Methods': 'POST, GET, OPTIONS, PUT, DELETE',
  'Access-Control-Max-Age': '86400',
}

/**
 * 处理CORS预检请求
 * @param req - 请求对象
 * @returns 如果是OPTIONS请求则返回CORS响应，否则返回null
 */
export function handleCors(req: Request): Response | null {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }
  return null
}

/**
 * 创建带有CORS头的响应
 * @param data - 响应数据
 * @param status - HTTP状态码
 * @param additionalHeaders - 额外的响应头
 * @returns 带有CORS头的Response对象
 */
export function createCorsResponse(
  data: any,
  status: number = 200,
  additionalHeaders: Record<string, string> = {}
): Response {
  const headers = {
    ...corsHeaders,
    'Content-Type': 'application/json',
    ...additionalHeaders,
  }

  return new Response(JSON.stringify(data), {
    status,
    headers,
  })
}

/**
 * 创建错误响应
 * @param message - 错误消息
 * @param status - HTTP状态码
 * @returns 错误响应
 */
export function createErrorResponse(message: string, status: number = 500): Response {
  return createCorsResponse(
    { error: message },
    status
  )
}
