/**
 * 电影详情Edge Function
 * 获取电影或电视剧的详细信息，包括演员、导演、制作公司等
 */

import { handleCors, createCorsResponse, createErrorResponse } from '../_shared/cors.ts'
import {
  getRequiredEnv,
  validateMethod,
  logRequest,
  retry
} from '../_shared/utils.ts'
import { detailsCache, generateDetailsCacheKey } from '../_shared/cache.ts'

// TMDB API配置
const TMDB_BASE_URL = 'https://api.themoviedb.org/3'
const TMDB_IMAGE_BASE_URL = 'https://image.tmdb.org/t/p'

// 接口定义
interface TMDBMovieDetails {
  id: number
  title?: string
  name?: string
  original_title?: string
  original_name?: string
  overview: string
  poster_path: string | null
  backdrop_path: string | null
  release_date?: string
  first_air_date?: string
  vote_average: number
  vote_count: number
  popularity: number
  genres: Array<{ id: number; name: string }>
  runtime?: number
  episode_run_time?: number[]
  budget?: number
  revenue?: number
  production_companies: Array<{
    id: number
    name: string
    logo_path: string | null
    origin_country: string
  }>
  production_countries: Array<{
    iso_3166_1: string
    name: string
  }>
  spoken_languages: Array<{
    english_name: string
    iso_639_1: string
    name: string
  }>
  status: string
  tagline: string
  adult: boolean
  original_language: string
  imdb_id?: string
  homepage?: string
  belongs_to_collection?: {
    id: number
    name: string
    poster_path: string | null
    backdrop_path: string | null
  }
  number_of_episodes?: number
  number_of_seasons?: number
}

interface TMDBCredits {
  cast: Array<{
    id: number
    name: string
    character: string
    profile_path: string | null
    order: number
  }>
  crew: Array<{
    id: number
    name: string
    job: string
    department: string
    profile_path: string | null
  }>
}

interface ProcessedMovieDetails {
  id: number
  title: string
  original_title: string
  overview: string
  poster_url: string | null
  backdrop_url: string | null
  release_date: string | null
  vote_average: number
  vote_count: number
  popularity: number
  genres: Array<{ id: number; name: string }>
  runtime: number | null
  budget: number | null
  revenue: number | null
  status: string
  tagline: string
  language: string
  imdb_id: string | null
  homepage: string | null
  production_companies: Array<{
    id: number
    name: string
    logo_url: string | null
    origin_country: string
  }>
  production_countries: Array<{
    code: string
    name: string
  }>
  spoken_languages: Array<{
    code: string
    name: string
    english_name: string
  }>
  collection: {
    id: number
    name: string
    poster_url: string | null
    backdrop_url: string | null
  } | null
  cast: Array<{
    id: number
    name: string
    character: string
    profile_url: string | null
    order: number
  }>
  crew: {
    directors: Array<{
      id: number
      name: string
      profile_url: string | null
    }>
    writers: Array<{
      id: number
      name: string
      profile_url: string | null
    }>
    producers: Array<{
      id: number
      name: string
      profile_url: string | null
    }>
  }
  media_type: 'movie' | 'tv'
  tv_info?: {
    number_of_episodes: number
    number_of_seasons: number
    episode_run_time: number[]
  }
}

/**
 * 处理图片URL
 */
function processImageUrl(path: string | null, size: string = 'w500'): string | null {
  if (!path) return null
  return `${TMDB_IMAGE_BASE_URL}/${size}${path}`
}

/**
 * 获取电影详情
 */
async function getMovieDetails(movieId: string, mediaType: 'movie' | 'tv'): Promise<TMDBMovieDetails> {
  const apiKey = getRequiredEnv('TMDB_API_KEY')

  const detailsUrl = `${TMDB_BASE_URL}/${mediaType}/${movieId}?language=zh-CN`

  console.log(`Fetching details: ${detailsUrl}`)

  const response = await fetch(detailsUrl, {
    method: 'GET',
    headers: {
      'Accept': 'application/json',
      'Authorization': `Bearer ${apiKey}`,
      'User-Agent': 'NarratoAI/1.0'
    }
  })

  if (!response.ok) {
    if (response.status === 404) {
      throw new Error('电影或电视剧不存在')
    }
    const errorText = await response.text()
    console.error(`TMDB API error: ${response.status} ${response.statusText}`, errorText)
    throw new Error(`TMDB API请求失败: ${response.status} ${response.statusText}`)
  }

  return await response.json()
}

/**
 * 获取演职人员信息
 */
async function getMovieCredits(movieId: string, mediaType: 'movie' | 'tv'): Promise<TMDBCredits> {
  const apiKey = getRequiredEnv('TMDB_API_KEY')

  const creditsUrl = `${TMDB_BASE_URL}/${mediaType}/${movieId}/credits?language=zh-CN`

  console.log(`Fetching credits: ${creditsUrl}`)

  const response = await fetch(creditsUrl, {
    method: 'GET',
    headers: {
      'Accept': 'application/json',
      'Authorization': `Bearer ${apiKey}`,
      'User-Agent': 'NarratoAI/1.0'
    }
  })

  if (!response.ok) {
    console.warn(`Credits API error: ${response.status} ${response.statusText}`)
    return { cast: [], crew: [] }
  }

  return await response.json()
}

/**
 * 处理详情数据
 */
function processDetailsData(details: TMDBMovieDetails, credits: TMDBCredits, mediaType: 'movie' | 'tv'): ProcessedMovieDetails {
  // 处理演员信息
  const cast = credits.cast.slice(0, 20).map(actor => ({
    id: actor.id,
    name: actor.name,
    character: actor.character,
    profile_url: processImageUrl(actor.profile_path, 'w185'),
    order: actor.order
  }))

  // 处理导演、编剧、制片人
  const directors = credits.crew.filter(person => person.job === 'Director').slice(0, 5).map(person => ({
    id: person.id,
    name: person.name,
    profile_url: processImageUrl(person.profile_path, 'w185')
  }))

  const writers = credits.crew.filter(person => 
    person.job === 'Writer' || person.job === 'Screenplay' || person.job === 'Story'
  ).slice(0, 5).map(person => ({
    id: person.id,
    name: person.name,
    profile_url: processImageUrl(person.profile_path, 'w185')
  }))

  const producers = credits.crew.filter(person => 
    person.job === 'Producer' || person.job === 'Executive Producer'
  ).slice(0, 5).map(person => ({
    id: person.id,
    name: person.name,
    profile_url: processImageUrl(person.profile_path, 'w185')
  }))

  const result: ProcessedMovieDetails = {
    id: details.id,
    title: details.title || details.name || '',
    original_title: details.original_title || details.original_name || '',
    overview: details.overview || '',
    poster_url: processImageUrl(details.poster_path),
    backdrop_url: processImageUrl(details.backdrop_path, 'w1280'),
    release_date: details.release_date || details.first_air_date || null,
    vote_average: details.vote_average || 0,
    vote_count: details.vote_count || 0,
    popularity: details.popularity || 0,
    genres: details.genres || [],
    runtime: details.runtime || (details.episode_run_time && details.episode_run_time[0]) || null,
    budget: details.budget || null,
    revenue: details.revenue || null,
    status: details.status || '',
    tagline: details.tagline || '',
    language: details.original_language || '',
    imdb_id: details.imdb_id || null,
    homepage: details.homepage || null,
    production_companies: (details.production_companies || []).map(company => ({
      id: company.id,
      name: company.name,
      logo_url: processImageUrl(company.logo_path, 'w185'),
      origin_country: company.origin_country
    })),
    production_countries: (details.production_countries || []).map(country => ({
      code: country.iso_3166_1,
      name: country.name
    })),
    spoken_languages: (details.spoken_languages || []).map(lang => ({
      code: lang.iso_639_1,
      name: lang.name,
      english_name: lang.english_name
    })),
    collection: details.belongs_to_collection ? {
      id: details.belongs_to_collection.id,
      name: details.belongs_to_collection.name,
      poster_url: processImageUrl(details.belongs_to_collection.poster_path),
      backdrop_url: processImageUrl(details.belongs_to_collection.backdrop_path, 'w1280')
    } : null,
    cast,
    crew: {
      directors,
      writers,
      producers
    },
    media_type: mediaType
  }

  // 如果是电视剧，添加电视剧特有信息
  if (mediaType === 'tv') {
    result.tv_info = {
      number_of_episodes: details.number_of_episodes || 0,
      number_of_seasons: details.number_of_seasons || 0,
      episode_run_time: details.episode_run_time || []
    }
  }

  return result
}

/**
 * 主处理函数
 */
Deno.serve(async (req: Request) => {
  try {
    // 处理CORS预检请求
    const corsResponse = handleCors(req)
    if (corsResponse) return corsResponse

    // 记录请求
    logRequest(req, 'movie-details')

    // 验证请求方法
    validateMethod(req, ['GET'])

    // 从URL参数获取电影ID和类型
    const url = new URL(req.url)
    const movieId = url.searchParams.get('id')
    const mediaType = (url.searchParams.get('type') as 'movie' | 'tv') || 'movie'
    
    if (!movieId) {
      return createErrorResponse('电影ID不能为空', 400)
    }

    if (!['movie', 'tv'].includes(mediaType)) {
      return createErrorResponse('媒体类型必须是movie或tv', 400)
    }

    // 验证ID格式
    if (!/^\d+$/.test(movieId)) {
      return createErrorResponse('电影ID格式错误', 400)
    }

    console.log(`Getting details for ${mediaType} ID: ${movieId}`)

    // 生成缓存键
    const cacheKey = generateDetailsCacheKey(movieId, mediaType, 'zh-CN')

    // 尝试从缓存获取结果
    const cachedResult = detailsCache.get(cacheKey)
    if (cachedResult) {
      console.log(`Cache hit for details: ${mediaType} ID ${movieId}`)
      return createCorsResponse({
        ...cachedResult,
        cached: true,
        timestamp: new Date().toISOString()
      })
    }

    // 并行获取详情和演职人员信息
    const [details, credits] = await Promise.all([
      retry(() => getMovieDetails(movieId, mediaType), 3, 1000),
      retry(() => getMovieCredits(movieId, mediaType), 2, 500)
    ])

    // 处理数据
    const processedDetails = processDetailsData(details, credits, mediaType)

    // 构建响应数据
    const responseData = {
      success: true,
      data: processedDetails,
      cached: false,
      timestamp: new Date().toISOString()
    }

    // 缓存结果
    detailsCache.set(cacheKey, responseData, 600000) // 缓存10分钟

    console.log(`Details fetched successfully for ${mediaType} "${processedDetails.title}"`)

    return createCorsResponse(responseData)

  } catch (error) {
    console.error('Movie details error:', error)
    
    // 根据错误类型返回不同的错误信息
    if (error.message.includes('TMDB_API_KEY')) {
      return createErrorResponse('服务配置错误，请联系管理员', 500)
    }
    
    if (error.message.includes('不存在')) {
      return createErrorResponse('电影或电视剧不存在', 404)
    }
    
    if (error.message.includes('TMDB API')) {
      return createErrorResponse('电影数据服务暂时不可用，请稍后重试', 503)
    }
    
    if (error.message.includes('not allowed')) {
      return createErrorResponse(error.message, 405)
    }

    return createErrorResponse('获取详情服务暂时不可用，请稍后重试', 500)
  }
})
