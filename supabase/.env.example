# Supabase Edge Functions 环境变量配置示例
# 复制此文件为 .env 并填入实际的值

# ===========================================
# TMDB API 配置 (必需)
# ===========================================
# 从 https://www.themoviedb.org/settings/api 获取
TMDB_API_KEY=your_tmdb_api_key_here

# ===========================================
# Supabase 配置 (通常自动提供)
# ===========================================
# 项目URL
SUPABASE_URL=https://your-project-id.supabase.co

# 匿名密钥 (用于客户端)
SUPABASE_ANON_KEY=your_anon_key_here

# 服务角色密钥 (用于服务端，权限更高)
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key_here

# ===========================================
# 本地开发配置
# ===========================================
# 本地Supabase实例URL (使用 supabase start 启动后)
# SUPABASE_URL=http://127.0.0.1:54321

# ===========================================
# 可选配置
# ===========================================
# OpenAI API密钥 (用于AI功能，暂未使用)
# OPENAI_API_KEY=your_openai_api_key_here

# 其他第三方API密钥
# ELEVENLABS_API_KEY=your_elevenlabs_api_key_here
# STABILITY_AI_API_KEY=your_stability_ai_api_key_here
