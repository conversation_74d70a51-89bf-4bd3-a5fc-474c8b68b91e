---
title: 调试
language_tabs:
  - shell: Shell
  - http: HTTP
  - javascript: JavaScript
  - ruby: Ruby
  - python: Python
  - php: PHP
  - java: Java
  - go: Go
toc_footers: []
includes: []
search: true
code_clipboard: true
highlight_theme: darkula
headingLevel: 2
generator: "@tarslib/widdershins v4.0.30"

---

# 调试

Base URLs:

# Authentication

# tmdb_api

## GET 搜索电影

GET /3/search/movie

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|query|query|string| 是 |none|
|include_adult|query|string| 是 |none|
|language|query|string| 是 |none|
|page|query|string| 是 |none|
|Authorization|header|string| 是 |none|
|accept|header|string| 是 |none|

> 返回示例

> 200 Response

```json
{
  "page": 1,
  "results": [
    {
      "adult": false,
      "backdrop_path": "/94cS0mzODEoNIXFT7nhPcI8V4IJ.jpg",
      "genre_ids": [
        878,
        28,
        12
      ],
      "id": 842675,
      "original_language": "zh",
      "original_title": "流浪地球2",
      "overview": "在并不遥远的未来，太阳急速衰老与膨胀，再过几百年整个太阳系将被它吞噬毁灭。为了应对这场史无前例的危机，地球各国放下芥蒂，成立联合政府，试图寻找人类存续的出路。通过摸索与考量，最终推着地球逃出太阳系的“移山计划”获得压倒性胜利。人们着手建造上万台巨大的行星发动机，带着地球踏上漫漫征程。满腔赤诚的刘培强和韩朵朵历经层层考验成为航天员大队的一员，并由此相知相恋。但是漫漫征途的前方，仿佛有一股神秘的力量不断破坏者人类的自救计划。看似渺小的刘培强、量子科学家图恒宇、联合政府中国代表周喆直以及无数平凡的地球人，构成了这项伟大计划的重要一环……本片根据刘慈欣同名科幻小说改编。",
      "popularity": 6.4546,
      "poster_path": "/cAS2e9hUwu6Ydsx7byXj16H00Ai.jpg",
      "release_date": "2023-01-22",
      "title": "流浪地球2",
      "video": false,
      "vote_average": 7.311,
      "vote_count": 628
    },
    {
      "adult": false,
      "backdrop_path": "/wIrqeoJHYtZmneqIufPtcOHMjOg.jpg",
      "genre_ids": [
        878,
        28,
        18
      ],
      "id": 535167,
      "original_language": "zh",
      "original_title": "流浪地球",
      "overview": "近未来，科学家们发现太阳急速衰老膨胀，短时间内包括地球在内的整个太阳系都将被太阳所吞没。为了自救，人类提出一个名为“流浪地球”的大胆计划，即倾全球之力在地球表面建造上万座发动机和转向发动机，推动地球离开太阳系，用2500年的时间奔往另外一个栖息之地。中国航天员刘培强在儿子刘启四岁那年前往国际空间站，和国际同侪肩负起领航者的重任。转眼刘启长大，他带着妹妹朵朵偷偷跑到地表，偷开外公韩子昂的运输车，结果不仅遭到逮捕，还遭遇了全球发动机停摆的事件。为了修好发动机，阻止地球坠入木星，全球开始展开饱和式营救，连刘启他们的车也被强征加入。在与时间赛跑的过程中，无数的人前仆后继，奋不顾身，只为延续百代子孙生存的希望……",
      "popularity": 4.8716,
      "poster_path": "/xFMScAZdxUdNYav0vdhV54v6ppT.jpg",
      "release_date": "2019-02-05",
      "title": "流浪地球",
      "video": false,
      "vote_average": 6.645,
      "vote_count": 1028
    },
    {
      "adult": false,
      "backdrop_path": "/si5tsXe72KbHuSCIsnHIp9TD1zz.jpg",
      "genre_ids": [
        878
      ],
      "id": 1381967,
      "original_language": "zh",
      "original_title": "流浪地球：飞跃2020特别版",
      "overview": "太阳即将毁灭，人类在地球表面建造出巨大的推进器，寻找新家园。然而宇宙之路危机四伏，为了拯救地球，为了人类能在漫长的2500年后抵达新家园，流浪地球时代的年轻人挺身而出，展开争分夺秒的生死之战",
      "popularity": 0.5978,
      "poster_path": "/kzRs3qB2Hd6gPmL8NKSHFs8E8CK.jpg",
      "release_date": "2020-11-26",
      "title": "流浪地球：飞跃2020特别版",
      "video": false,
      "vote_average": 9,
      "vote_count": 1
    },
    {
      "adult": false,
      "backdrop_path": "/n3XvbFpRbTSu4olOwu0Rjuirrdv.jpg",
      "genre_ids": [
        99
      ],
      "id": 1328632,
      "original_language": "zh",
      "original_title": "流浪地球2：再次冒险",
      "overview": "　　该纪录片将带观众深入探索《流浪地球2》的幕后世界：导演、制片人、美术及特效团队等电影核心主创将亲自讲述这部科幻巨制的制作过程与他们的心路历程，观众将看到他们在追求电影艺术极致的同时，如何推动中国电影工业化进程、为中国科幻电影的未来开辟新道路。\r 　　此外，纪录片还特别邀请了中国知名科幻学者、作家回顾中国科幻的发展历程——从早期的科幻小说到如今的科幻电影，观众将了解到百年时光中，中国科幻从萌芽到茁壮成长的过程。这是一次不容错过的中国科幻电影冒险之旅，也是一次对中国科幻未来的深情展望。",
      "popularity": 0.7504,
      "poster_path": "/zQP6iW5Ya4MyMRXl0t28ckV1spp.jpg",
      "release_date": "2024-09-15",
      "title": "流浪地球2：再次冒险",
      "video": false,
      "vote_average": 7,
      "vote_count": 2
    },
    {
      "adult": false,
      "backdrop_path": "/cCzMePim8EXRHExizmBhJC063Ju.jpg",
      "genre_ids": [
        878,
        28,
        12
      ],
      "id": 1231322,
      "original_language": "zh",
      "original_title": "流浪地球3",
      "overview": "太阳即将毁灭，人类在地球表面建造出巨大的推进器，寻找新的家园。然而宇宙之路危机四伏，为了拯救地球，流浪地球时代的年轻人第三次挺身而出，展开争分夺秒的生死之战……",
      "popularity": 1.5479,
      "poster_path": "/6J47ApJVOmNrHJezrxnRaTDXBfx.jpg",
      "release_date": "2027-02-06",
      "title": "流浪地球3",
      "video": false,
      "vote_average": 0,
      "vote_count": 0
    }
  ],
  "total_pages": 1,
  "total_results": 5
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» page|integer|true|none||none|
|» results|[object]|true|none||none|
|»» adult|boolean|true|none||none|
|»» backdrop_path|string|true|none||none|
|»» genre_ids|[integer]|true|none||none|
|»» id|integer|true|none||none|
|»» original_language|string|true|none||none|
|»» original_title|string|true|none||none|
|»» overview|string|true|none||none|
|»» popularity|number|true|none||none|
|»» poster_path|string|true|none||none|
|»» release_date|string|true|none||none|
|»» title|string|true|none||none|
|»» video|boolean|true|none||none|
|»» vote_average|number|true|none||none|
|»» vote_count|integer|true|none||none|
|» total_pages|integer|true|none||none|
|» total_results|integer|true|none||none|

## GET 电影详情

GET /3/movie/535167

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|language|query|string| 是 |none|
|Authorization|header|string| 是 |none|
|accept|header|string| 是 |none|

> 返回示例

> 200 Response

```json
{
  "adult": false,
  "backdrop_path": "/wIrqeoJHYtZmneqIufPtcOHMjOg.jpg",
  "belongs_to_collection": {
    "id": 1073029,
    "name": "流浪地球（系列）",
    "poster_path": "/6qWFQkZfCmDmaAIQlXlBJWNga3k.jpg",
    "backdrop_path": "/dDG37QLsERXFowAOfWYLwcsag4r.jpg"
  },
  "budget": 48000000,
  "genres": [
    {
      "id": 878,
      "name": "科幻"
    },
    {
      "id": 28,
      "name": "动作"
    },
    {
      "id": 18,
      "name": "剧情"
    }
  ],
  "homepage": "",
  "id": 535167,
  "imdb_id": "tt7605074",
  "origin_country": [
    "CN"
  ],
  "original_language": "zh",
  "original_title": "流浪地球",
  "overview": "近未来，科学家们发现太阳急速衰老膨胀，短时间内包括地球在内的整个太阳系都将被太阳所吞没。为了自救，人类提出一个名为“流浪地球”的大胆计划，即倾全球之力在地球表面建造上万座发动机和转向发动机，推动地球离开太阳系，用2500年的时间奔往另外一个栖息之地。中国航天员刘培强在儿子刘启四岁那年前往国际空间站，和国际同侪肩负起领航者的重任。转眼刘启长大，他带着妹妹朵朵偷偷跑到地表，偷开外公韩子昂的运输车，结果不仅遭到逮捕，还遭遇了全球发动机停摆的事件。为了修好发动机，阻止地球坠入木星，全球开始展开饱和式营救，连刘启他们的车也被强征加入。在与时间赛跑的过程中，无数的人前仆后继，奋不顾身，只为延续百代子孙生存的希望……",
  "popularity": 4.8716,
  "poster_path": "/xFMScAZdxUdNYav0vdhV54v6ppT.jpg",
  "production_companies": [
    {
      "id": 14714,
      "logo_path": "/dSHaVKtBCpMU5VP9wMbTkqov62i.png",
      "name": "China Film Group Corporation",
      "origin_country": "CN"
    },
    {
      "id": 99538,
      "logo_path": null,
      "name": "Beijing Jingxi Culture",
      "origin_country": ""
    },
    {
      "id": 65442,
      "logo_path": "/mAAUTVBF4KGW4QxESHWg4N8pLdF.png",
      "name": "DF Pictures",
      "origin_country": "CN"
    },
    {
      "id": 115322,
      "logo_path": null,
      "name": "G!Film Studio",
      "origin_country": ""
    },
    {
      "id": 186583,
      "logo_path": null,
      "name": "Free Whale Pictures",
      "origin_country": "CN"
    }
  ],
  "production_countries": [
    {
      "iso_3166_1": "CN",
      "name": "China"
    }
  ],
  "release_date": "2019-02-05",
  "revenue": 0,
  "runtime": 126,
  "spoken_languages": [
    {
      "english_name": "Mandarin",
      "iso_639_1": "zh",
      "name": "普通话"
    },
    {
      "english_name": "English",
      "iso_639_1": "en",
      "name": "English"
    },
    {
      "english_name": "French",
      "iso_639_1": "fr",
      "name": "Français"
    },
    {
      "english_name": "Hindi",
      "iso_639_1": "hi",
      "name": "हिन्दी"
    },
    {
      "english_name": "Indonesian",
      "iso_639_1": "id",
      "name": "Bahasa indonesia"
    },
    {
      "english_name": "Japanese",
      "iso_639_1": "ja",
      "name": "日本語"
    },
    {
      "english_name": "Korean",
      "iso_639_1": "ko",
      "name": "한국어/조선말"
    },
    {
      "english_name": "Russian",
      "iso_639_1": "ru",
      "name": "Pусский"
    }
  ],
  "status": "Released",
  "tagline": "带着家园流浪 是最终极的浪漫",
  "title": "流浪地球",
  "video": false,
  "vote_average": 6.645,
  "vote_count": 1028
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» adult|boolean|true|none||none|
|» backdrop_path|string|true|none||none|
|» belongs_to_collection|object|true|none||none|
|»» id|integer|true|none||none|
|»» name|string|true|none||none|
|»» poster_path|string|true|none||none|
|»» backdrop_path|string|true|none||none|
|» budget|integer|true|none||none|
|» genres|[object]|true|none||none|
|»» id|integer|true|none||none|
|»» name|string|true|none||none|
|» homepage|string|true|none||none|
|» id|integer|true|none||none|
|» imdb_id|string|true|none||none|
|» origin_country|[string]|true|none||none|
|» original_language|string|true|none||none|
|» original_title|string|true|none||none|
|» overview|string|true|none||none|
|» popularity|number|true|none||none|
|» poster_path|string|true|none||none|
|» production_companies|[object]|true|none||none|
|»» id|integer|true|none||none|
|»» logo_path|string¦null|true|none||none|
|»» name|string|true|none||none|
|»» origin_country|string|true|none||none|
|» production_countries|[object]|true|none||none|
|»» iso_3166_1|string|false|none||none|
|»» name|string|false|none||none|
|» release_date|string|true|none||none|
|» revenue|integer|true|none||none|
|» runtime|integer|true|none||none|
|» spoken_languages|[object]|true|none||none|
|»» english_name|string|true|none||none|
|»» iso_639_1|string|true|none||none|
|»» name|string|true|none||none|
|» status|string|true|none||none|
|» tagline|string|true|none||none|
|» title|string|true|none||none|
|» video|boolean|true|none||none|
|» vote_average|number|true|none||none|
|» vote_count|integer|true|none||none|

# 数据模型

